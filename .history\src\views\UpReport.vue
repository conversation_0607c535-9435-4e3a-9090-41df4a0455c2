<template>
  <div class="upreport-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 顶部标题区域 -->
    <div class="header-section">
      <div class="header-card">
        <div class="medical-icon">📤</div>
        <h1 class="page-title">上传报告</h1>
        <p class="page-subtitle">Upload Medical Report</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { showToast } from 'vant';
import { useRouter } from 'vue-router';

const router = useRouter();

// 表单数据
const formData = reactive({
  medicalDate: '',
  hospital: '',
  reportType: '',
  fileList: []
});

// 日期选择器
const showDatePicker = ref(false);
const selectedDate = ref(new Date());
const minDate = new Date(2025, 1, 1);
const maxDate = new Date();

// 报告类型选择器
const showReportTypePicker = ref(false);
const selectedReportType = ref('');
const reportTypeOptions = [
  '血常规检查',
  '生化指标检查',
  '肿瘤指标检查',
  '胸部CT',
  '腹部CT',
  '盆腔CT'
];

// 上传状态
const uploading = ref(false);
const showLogDialog = ref(false);
const processingLogs = ref([]);
const uploadCompleted = ref(false);

// 日期确认
const onDateConfirm = (value) => {
  const date = new Date(value);
  formData.medicalDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  showDatePicker.value = false;
};

// 报告类型确认
const onReportTypeConfirm = (value) => {
  // Vant 4.x 版本中，picker 的 confirm 事件会传递一个对象
  // 包含 selectedValues 数组，我们需要取第一个元素
  formData.reportType = value.selectedValues[0];
  showReportTypePicker.value = false;
};

// 图片上传后处理
const afterRead = (file) => {
  // 这里可以添加图片预处理逻辑
  console.log('文件已选择:', file);
};

// 删除图片前确认
const beforeDelete = () => {
  return true; // 返回 true 表示允许删除
};

// 提交表单
const onSubmit = () => {
  if (formData.fileList.length === 0) {
    showToast('请至少上传一张报告图片');
    return;
  }

  uploading.value = true;
  showLogDialog.value = true;
  processingLogs.value = [];
  uploadCompleted.value = false;

  // 模拟上传过程和日志显示
  addLog('开始处理上传请求...', 'pending');
  
  setTimeout(() => {
    addLog('验证报告信息...', 'success');
    
    setTimeout(() => {
      addLog('上传图片中...', 'pending');
      
      setTimeout(() => {
        addLog('图片上传完成', 'success');
        
        setTimeout(() => {
          addLog('解析报告内容...', 'pending');
          
          setTimeout(() => {
            addLog('提取医疗指标数据...', 'pending');
            
            setTimeout(() => {
              addLog('数据处理完成', 'success');
              
              setTimeout(() => {
                addLog('报告上传成功！', 'success');
                uploadCompleted.value = true;
                uploading.value = false;
              }, 800);
            }, 1200);
          }, 1500);
        }, 1000);
      }, 1500);
    }, 800);
  }, 500);
};

// 添加日志
const addLog = (message, status) => {
  const icons = {
    pending: 'clock-o',
    success: 'checked',
    error: 'warning-o'
  };
  
  processingLogs.value.push({
    message,
    status,
    icon: icons[status] || 'info-o'
  });
};

// 完成上传
const finishUpload = () => {
  // 重置表单
  formData.medicalDate = '';
  formData.hospital = '';
  formData.reportType = '';
  formData.fileList = [];
  
  showLogDialog.value = false;
  showToast('报告上传成功');
  router.push('/reportView');
};
</script>

<style scoped>
.upreport-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 动态背景元素 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.08);
  font-size: 20px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 5%; left: 8%; }
.floating-cross:nth-child(2) { top: 15%; right: 12%; }
.floating-cross:nth-child(3) { top: 45%; left: 3%; }
.floating-cross:nth-child(4) { top: 65%; right: 8%; }
.floating-cross:nth-child(5) { top: 25%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 35%; left: 15%; }
.floating-cross:nth-child(7) { bottom: 15%; right: 20%; }
.floating-cross:nth-child(8) { top: 80%; left: 70%; }

.floating-circle {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 12%; left: 50%; }
.floating-circle:nth-child(10) { top: 55%; right: 25%; }
.floating-circle:nth-child(11) { bottom: 25%; left: 60%; }
.floating-circle:nth-child(12) { top: 35%; right: 5%; }
.floating-circle:nth-child(13) { bottom: 45%; left: 35%; }
.floating-circle:nth-child(14) { top: 75%; right: 45%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  animation: heartbeat 4s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 30%; left: 20%; }
.floating-heart:nth-child(16) { top: 60%; right: 15%; }
.floating-heart:nth-child(17) { bottom: 20%; left: 80%; }
.floating-heart:nth-child(18) { top: 85%; left: 40%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.08; }
  50% { transform: scale(1.3); opacity: 0.15; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); opacity: 0.06; }
  50% { transform: scale(1.1); opacity: 0.12; }
}

/* 顶部标题区域 */
.header-section {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

.header-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.medical-icon {
  font-size: 36px;
  margin-bottom: 10px;
}

.page-title {
  color: white;
  font-size: 24px;
  margin: 0;
  font-weight: 600;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5px;
  font-size: 14px;
}

/* 表单区域 */
.form-section {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

:deep(.van-cell-group--inset) {
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
}

:deep(.van-field__label) {
  color: #333;
  font-weight: 500;
}

:deep(.van-cell) {
  background-color: rgba(255, 255, 255, 0.9);
}

/* 上传区域 */
.upload-section {
  margin-top: 20px;
  padding: 0 16px;
}

.upload-button {
  width: 100%;
  height: 120px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #666;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 提交按钮 */
.submit-section {
  margin-top: 30px;
  padding: 0 16px;
}

:deep(.van-button--primary) {
  background: linear-gradient(45deg, #4481eb, #04befe);
  border: none;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

/* 日志弹窗 */
:deep(.log-dialog .van-dialog__header) {
  padding: 20px 0;
  font-weight: 600;
  color: #333;
}

.log-content {
  padding: 0 20px 20px;
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
  /* 状态颜色统一到图标的class */
}

.log-item .van-icon.pending { color: #ff9800; }
.log-item .van-icon.success { color: #4caf50; }
.log-item .van-icon.error { color: #f44336; }

.log-item .van-icon {
  margin-right: 10px;
  font-size: 18px;
}

.log-item .pending {
  color: #ff9800;
}

.log-item .success {
  color: #4caf50;
}

.log-item .error {
  color: #f44336;
}

.dialog-footer {
  padding: 0 20px 20px;
}

/* 弹出层样式 */
:deep(.van-popup) {
  max-height: 50vh;
  overflow: hidden;
}

:deep(.van-picker) {
  width: 100%;
}

:deep(.van-picker-column) {
  font-size: 16px;
}

:deep(.van-datetime-picker) {
  width: 100%;
}

:deep(.van-overlay) {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>

