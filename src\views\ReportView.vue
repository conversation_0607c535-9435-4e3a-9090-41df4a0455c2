<template>
  <div class="report-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 顶部标题区域
    <div class="header-section">
      <div class="header-card">
        <div class="medical-icon">📋</div>
        <h1 class="page-title">报告管理</h1>
        <p class="page-subtitle">Medical Report Management</p>
      </div>
    </div> -->

    <back-button title="报告管理" />

    <!-- 搜索筛选区域 -->
    <div class="search-section" v-if="showSearch">
      <div class="search-card">
        <van-field
          v-model="searchForm.date"
          readonly
          clickable
          label="日期"
          placeholder="选择日期"
          @click="showDatePicker = true"
          right-icon="arrow-down"
        />
        <van-field
          v-model="searchForm.location"
          label="地点"
          placeholder="请输入检查地点"
          clearable
        />
        <div class="search-actions">
          <van-button type="primary" size="small" @click="searchReports">搜索</van-button>
          <van-button size="small" @click="resetSearch">重置</van-button>
        </div>
      </div>
    </div>

    <!-- 切换按钮 -->
    <!-- <div class="toggle-section">
      <van-button 
        :type="showSearch ? 'default' : 'primary'" 
        size="small" 
        @click="toggleSearchMode"
        icon="search"
      >
        {{ showSearch ? '隐藏筛选' : '显示筛选' }}
      </van-button>
    </div> -->

    <!-- 报告列表 -->
    <div class="list-section">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多数据了"
          loading-text="加载中..."
          @load="onLoad"
        >
          <div 
            v-for="item in reportList" 
            :key="item.id" 
            class="report-item"
            @click="viewDetail(item)"
          >
            <div class="report-header">
              <div class="report-title">{{ item.medical_type || '常规检查' }}</div>
            </div>
            <div class="report-info">
              <div class="info-item">
                <span class="label">医院：</span>
                <span class="value">{{ item.hospital || '未知' }}</span>
                <div class="report-date">{{ formatDate(item.medical_date) }}</div>
                <!-- <van-tag :type="getStatusType(item.status)">{{ getStatusText(item.status) }}</van-tag> -->
              </div>
            </div>
            <div class="report-info">
              <div class="info-item">
                <span class="label">备注：</span>
                <span class="value">{{ item.comment || '无' }}</span>
              </div>
            </div>
            <!-- <div class="report-status">
              <van-tag :type="getStatusType(item.status)">{{ getStatusText(item.status) }}</van-tag>
            </div> -->
          </div>
        </van-list>
        
        <!-- 添加回到顶部按钮 -->
        <van-back-top />

      </van-pull-refresh>
    </div>

    <!-- 详情弹窗 -->
    <van-popup 
      v-model:show="showDetail" 
      position="bottom" 
      :style="{ height: '80%' }"
      round
      closeable
      @opened="resetDetailScroll"
    >
      <div class="detail-container" ref="detailContainer">
        <div class="detail-header">
          <h3>检查详情</h3>
        </div>
        <div class="detail-content" v-if="currentDetail">
          <!-- <van-cell-group>
            <van-cell title="检查类型" :value="currentDetail.medical_type" />
            <van-cell title="检查日期" :value="formatDate(currentDetail.medical_date)" />
            <van-cell title="医院" :value="currentDetail.hospital" />
            <van-cell title="状态" :value="getStatusText(currentDetail.status)" />
          </van-cell-group> -->
          
          <!-- 检查结果表格 -->
          <div class="result-section" v-if="currentDetail.results">
            <!-- <h4>检查结果</h4> -->
            <div class="result-table">
              <div 
                v-for="(result, index) in currentDetail.results" 
                :key="index"
                class="result-row"
              >
                <div class="result-left">
                  <div class="result-item">{{ result.item }}</div>
                  <div class="result-unit" v-if="result.unit">{{ result.unit }}</div>
                </div>
                <div class="result-right">
                  <div class="result-value" :class="getResultClass(result.status)">
                    {{ result.value }}{{ result.status === 'low' ? ' ↓' : result.status === 'high' ? ' ↑' : '' }}
                  </div>
                  <div class="result-reference">{{ result.reference }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 医生建议 -->
          <div class="advice-section" v-if="currentDetail.advice">
            <h4>医生建议</h4>
            <div class="advice-content">{{ currentDetail.advice }}</div>
          </div>
        </div>
      </div>
    </van-popup>

  </div>
</template>

<script setup>
import BackButton from '@/components/index-detail/BackButton.vue';
import { ref, reactive, onMounted, nextTick } from 'vue';
import { showToast } from 'vant';
import { useRouter } from 'vue-router';
import { getMedicalCheckList, getMedicalCheckDetail, getMedicalCheckDetailByFilter } from '@/api/medical';

const router = useRouter();

// 响应式数据
const reportList = ref([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const showDetail = ref(false);
const showSearch = ref(false);
const showDatePicker = ref(false);
const currentDetail = ref(null);
const detailContainer = ref(null);

// 重置详情弹窗的滚动位置
const resetDetailScroll = () => {
  if (detailContainer.value) {
    detailContainer.value.scrollTop = 0;
  }
};

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
});

// 搜索表单
const searchForm = reactive({
  date: '',
  location: ''
});

// 方法
const onLoad = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    const response = await getMedicalCheckList(params);
    
    if (response.data) {
      const newData = response.data.data || []; 
      
      // 修复分页逻辑
      pagination.total = response.data.pagination?.total || 0;
      
      if (refreshing.value) {
        reportList.value = newData;
        refreshing.value = false;
      } else {
        reportList.value = [...reportList.value, ...newData];
      }
      // 判断是否还有更多数据
      finished.value = pagination.page >= pagination.total;
      pagination.page++;
    }
  } catch (error) {
    console.error('获取报告列表失败:', error);
    showToast('网络错误，请稍后重试');
    finished.value = true;
  } finally {
    loading.value = false;
  }
};

const onRefresh = async () => {
  try {
    refreshing.value = true;
    pagination.page = 1;
    finished.value = false;
    reportList.value = [];
    await onLoad();
    // showToast('刷新成功');
  } catch (error) {
    // showToast('刷新失败');
  } finally {
    refreshing.value = false;
  }
};

const viewDetail = async (item) => {
  try {
    loading.value = true;
    const response = await getMedicalCheckDetail(item.medical_id);
    
    if (response.data) {
      currentDetail.value = {
        ...item,
        results: response.data.results.map(r => ({
          item: r.index_name,
          value: r.index_value,
          unit: r.index_unit,
          reference: r.reference_value,
          status: r.index_status
        })) || []
      };
      showDetail.value = true;
      // 确保下次打开时重置滚动位置
      nextTick(() => {
        if (detailContainer.value) {
          detailContainer.value.scrollTop = 0;
        }
      });
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    showToast('获取详情失败');
  } finally {
    loading.value = false;
  }
};

const toggleSearchMode = () => {
  showSearch.value = !showSearch.value;
};

const searchReports = async () => {
  try {
    loading.value = true;
    const searchParams = {
      date: searchForm.date,
      location: searchForm.location
    };
    
    const response = await getMedicalCheckDetailByFilter(searchParams);
    
    if (response.code === 200) {
      reportList.value = response.data || [];
      finished.value = true; // 搜索结果不分页
      showToast('搜索完成');
    } else {
      showToast(response.message || '搜索失败');
    }
  } catch (error) {
    console.error('搜索失败:', error);
    showToast('搜索失败');
  } finally {
    loading.value = false;
  }
};

const resetSearch = () => {
  searchForm.date = '';
  searchForm.location = '';
  // 重置搜索后重新加载数据
  pagination.page = 1;
  finished.value = false;
  reportList.value = [];
  onLoad();
};

// 同时修改 onDateConfirm 方法以正确处理数组格式
const onDateConfirm = (value) => {
  // value 现在是一个数组 [year, month, day]
  if (Array.isArray(value) && value.length >= 3) {
    const date = new Date(value[0], value[1], value[2]);
    searchForm.date = formatDate(date);
  }
  showDatePicker.value = false;
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

const getStatusType = (status) => {
  const types = {
    normal: 'success',
    abnormal: 'danger',
    pending: 'warning'
  };
  return types[status] || 'default';
};

const getStatusText = (status) => {
  const texts = {
    normal: '正常',
    abnormal: '异常',
    pending: '待审核'
  };
  return texts[status] || '未知';
};

const getResultClass = (status) => {
  return {
    'result-normal': status === 'normal',
    'result-high': status === 'high',
    'result-low': status === 'low'
  };
};

onMounted(() => {
  onLoad();
});
</script>

<style scoped>
.report-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 动态背景元素 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 8%; left: 10%; }
.floating-cross:nth-child(2) { top: 20%; right: 15%; }
.floating-cross:nth-child(3) { top: 50%; left: 5%; }
.floating-cross:nth-child(4) { top: 70%; right: 10%; }
.floating-cross:nth-child(5) { top: 35%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 30%; left: 20%; }
.floating-cross:nth-child(7) { bottom: 10%; right: 25%; }
.floating-cross:nth-child(8) { top: 85%; left: 65%; }

.floating-circle {
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.06);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 15%; left: 55%; }
.floating-circle:nth-child(10) { top: 60%; right: 30%; }
.floating-circle:nth-child(11) { bottom: 20%; left: 70%; }
.floating-circle:nth-child(12) { top: 40%; right: 8%; }
.floating-circle:nth-child(13) { bottom: 40%; left: 40%; }
.floating-circle:nth-child(14) { top: 80%; right: 50%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.04);
  font-size: 16px;
  animation: heartbeat 5s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 25%; left: 25%; }
.floating-heart:nth-child(16) { top: 65%; right: 20%; }
.floating-heart:nth-child(17) { bottom: 15%; left: 75%; }
.floating-heart:nth-child(18) { top: 90%; left: 45%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-12px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.06; }
  50% { transform: scale(1.2); opacity: 0.12; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.05); }
  50% { transform: scale(1); }
  75% { transform: scale(1.02); }
}

/* 页面内容 */
.header-section {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

.header-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.medical-icon {
  font-size: 36px;
  margin-bottom: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.page-title {
  color: #2c5aa0;
  font-size: 22px;
  font-weight: 600;
  margin: 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  color: #6b7280;
  font-size: 12px;
  margin: 0;
  font-weight: 300;
}

.search-section {
  position: relative;
  z-index: 2;
  margin-bottom: 15px;
}

.search-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.search-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.toggle-section {
  position: relative;
  z-index: 2;
  text-align: center;
  margin-bottom: 20px;
}

.list-section {
  position: relative;
  z-index: 2;
  height: calc(100vh - 50px); /* 调整为更合适的高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增加iOS滚动流畅度 */
}

.report-item {
  background: rgba(255, 255, 255, 0.95);
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
}

.report-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.report-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c5aa0;
}

.report-date {
  font-size: 12px;
  color: #8b9dc3;
}

.report-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
}

.label {
  color: #6b7280;
  width: 50px;
  flex-shrink: 0;
}

.value {
  color: #374151;
  flex: 1;
}

.report-status {
  text-align: right;
}

/* 详情弹窗 */
.detail-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.detail-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e7eb;
}

.detail-header h3 {
  color: #2c5aa0;
  font-size: 18px;
  margin: 0;
}

.result-section {
  margin-top: 20px;
}

.result-section h4 {
  color: #2c5aa0;
  font-size: 16px;
  margin-bottom: 15px;
}

.result-table {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.result-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

.result-row:last-child {
  border-bottom: none;
}

.result-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.result-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  text-align: right;
  width: 40%;
}

.result-item {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.result-value {
  font-weight: 700;
  font-size: 16px;
}

.result-normal { color: #10b981; }
.result-high { color: #ef4444; }
.result-low { color: #f59e0b; }

.result-unit {
  color: #6b7280;
  font-size: 12px;
  font-weight: 400;
}

.result-reference {
  color: #8b9dc3;
  font-size: 12px;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-row {
    /* flex-direction: column; */
    gap: 8px;
  }
  
  .result-right {
    align-items: flex-start;
    text-align: left;
  }
}

@media (max-width: 480px) {
  .report-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .medical-icon {
    font-size: 32px;
  }
}
</style>
