<template>
  <div class="index-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 顶部标题区域 -->
    <!-- <div class="header-section">
      <div class="header-card">
        <div class="medical-icon">📊</div>
        <h1 class="page-title">指标查询</h1>
        <p class="page-subtitle">Medical Index Query</p>
      </div>
    </div> -->

    <back-button title="指标查询" />

    <!-- 指标类型过滤区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <van-tabs v-model:active="activeTab" @change="onTabChange">
          <van-tab 
            v-for="type in typeList" 
            :key="type.type_id" 
            :title="type.type_name"
            :name="type.type_id"
          />
        </van-tabs>
      </div>
    </div>

    <!-- 指标列表 -->
    <div class="list-section" ref="listSectionRef">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多数据了"
        loading-text="加载中..."
      >
        <!-- 添加多选模式切换按钮 -->
        <div class="compare-mode-controls">
          <van-button 
            class="compare-toggle-btn"
            :class="{ active: compareMode }"
            icon="cluster-o"
            size="small" 
            round
            @click="toggleCompareMode"
          >
            {{ compareMode ? '退出对比' : '指标对比' }}
          </van-button>
          
          <transition name="fade">
            <van-button 
              v-if="compareMode && selectedIndexes.length > 1" 
              class="start-compare-btn"
              icon="chart-trending-o"
              size="small" 
              type="primary"
              round
              @click="compareSelected"
            >
              开始对比({{selectedIndexes.length}})
            </van-button>
          </transition>
        </div>

        <van-swipe-cell v-for="item in indexList" :key="item.index_id">
          <div 
            class="index-item"
            :class="{ 'selected-item': compareMode && isIndexSelected(item) }"
            @click="compareMode ? toggleSelectIndex(item) : viewDetail(item)"
          >
            <!-- 修改选择标记和内容的布局 -->
            <div class="item-container">
              <div v-if="compareMode" class="checkbox-container">
                <van-checkbox 
                  :checked="isIndexSelected(item)"
                  @click.stop="toggleSelectIndex(item)"
                  icon-size="20px"
                />
              </div>
              <div class="index-content">
                <div class="index-name">{{ item.index_name }}</div>
                <div class="index-desc">{{ item.description || '无描述信息' }}</div>
              </div>
            </div>
            <van-icon name="arrow-right" class="arrow-icon" />
          </div>
          <template #right>
            <van-button 
              :type="activeTab === 1 ? 'danger' : 'primary'" 
              square 
              class="swipe-button"
              @click.stop="handleFavorite(item)"
            >
              {{ activeTab === 1 ? '取消收藏' : '收藏' }}
            </van-button>
          </template>
        </van-swipe-cell>
      </van-list>
      <van-empty v-if="indexList.length === 0" description="暂无数据" />
      <van-back-top 
        :offset="100"
      />
    </div>

    <!-- 类型选择器 -->
    <!-- <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeColumns"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
        value-key="value"
        title-key="text"
      />
    </van-popup> -->
  </div>
</template>

<script>
// 使用选项式API定义组件名称
export default {
  name: 'IndexView'
};
</script>

<script setup>
import { ref, onMounted, computed, onActivated, nextTick } from 'vue';
import { showToast } from 'vant';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { getMedicalIndex, getMedicalTypes, addFavoriteIndex, removeFavoriteIndex } from '@/api/medical';
import BackButton from '@/components/index-detail/BackButton.vue';

const router = useRouter();
const store = useStore();

// 响应式数据
const indexList = ref([]);
const loading = ref(false);
const finished = ref(false);
const typeList = ref([]); // 存储类型列表
const activeTab = ref(1); // 默认选中

// 添加滚动位置记忆
const scrollPosition = ref(0);
const listSectionRef = ref(null);

// 从Vuex获取用户ID
const userId = computed(() => store.getters.userId || 1);

// 获取指标类型数据
const fetchTypeData = async () => {
  try {
    const response = await getMedicalTypes();
    if (response.data.data) {
      typeList.value = response.data.data;
    } else {
      showToast(response.message || '获取指标类型失败');
    }
  } catch (error) {
    console.error('获取指标类型失败:', error);
    showToast('网络错误，请稍后重试');
  }
};

// 标签页切换处理
const onTabChange = (index) => {
  activeTab.value = index;
  fetchIndexData();
};

// 获取指标数据
const fetchIndexData = async () => {
  try {
    loading.value = true;
    const params = {
      medical_type: activeTab.value, // 传递选中的类型ID
      user_id: userId.value
    };
    
    const response = await getMedicalIndex(params);
    
    if (response.data) {
      indexList.value = response.data || [];
      finished.value = true;
    } else {
      showToast(response.message || '获取指标数据失败');
    }
  } catch (error) {
    console.error('获取指标列表失败:', error);
    showToast('网络错误，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 查看指标详情前保存滚动位置
const viewDetail = (item) => {
  saveScrollPosition(); // 保存当前滚动位置
  router.push({
    path: '/indexDetail',
    query: {
      index_id: item.index_id,
      index_name: item.index_name,
      is_chart: item.is_chart || 0,
      reference_max: item.reference_max || '',
      reference_min: item.reference_min || 0
    }
  });
};

// 处理收藏/取消收藏
const handleFavorite = async (item) => {
  try {
    const params = {
      user_id: userId.value,
      index_id: item.index_id
    };
    
    if (activeTab.value === 1) {
      // 取消收藏逻辑
      const response = await removeFavoriteIndex(params);
      if (response.data && response.data.status === 'success') {
        showToast('已取消收藏');
        // 重新加载数据
        fetchIndexData();
      } else {
        showToast(response.data?.message || '取消收藏失败');
      }
    } else {
      // 收藏逻辑
      const response = await addFavoriteIndex(params);
      if (response.data && response.data.status === 'success') {
        showToast('已收藏');
        // 如果需要刷新当前列表
        fetchIndexData();
      } else {
        showToast(response.data?.message || '收藏失败');
      }
    }
  } catch (error) {
    console.error('操作失败:', error);
    showToast('操作失败，请稍后重试');
  }
};

// 保存滚动位置
const saveScrollPosition = () => {
  if (listSectionRef.value) {
    scrollPosition.value = listSectionRef.value.scrollTop;
  }
};

// 恢复滚动位置
const restoreScrollPosition = () => {
  nextTick(() => {
    if (listSectionRef.value && scrollPosition.value > 0) {
      listSectionRef.value.scrollTop = scrollPosition.value;
    }
  });
};

// 在组件激活时恢复滚动位置（从详情页返回时触发）
onActivated(() => {
  restoreScrollPosition();
})

onMounted(() => {
  fetchTypeData(); // 先获取类型数据
  fetchIndexData(); // 然后获取指标数据
});

// 添加多选相关状态
const compareMode = ref(false);
const selectedIndexes = ref([]);

// 切换对比模式
const toggleCompareMode = () => {
  compareMode.value = !compareMode.value;
  if (!compareMode.value) {
    selectedIndexes.value = [];
  }
};

// 切换选择指标
const toggleSelectIndex = (item) => {
  const index = selectedIndexes.value.findIndex(i => i.index_id === item.index_id);
  if (index > -1) {
    selectedIndexes.value.splice(index, 1);
  } else {
    // 限制最多选择2个指标
    if (selectedIndexes.value.length < 2) {
      selectedIndexes.value.push(item);
    } else {
      showToast('最多只能对比2个指标');
    }
  }
};

// 检查指标是否被选中
const isIndexSelected = (item) => {
  return selectedIndexes.value.some(i => i.index_id === item.index_id);
};

// 开始对比
const compareSelected = () => {
  if (selectedIndexes.value.length < 2) {
    showToast('请至少选择2个指标进行对比');
    return;
  }
  
  saveScrollPosition();
  router.push({
    path: '/indexDetail',
    query: {
      compare_mode: '1',
      indexes: JSON.stringify(selectedIndexes.value.map(item => ({
        index_id: item.index_id,
        index_name: item.index_name,
        is_chart: item.is_chart || 0,
        reference_max: item.reference_max || '',
        reference_min: item.reference_min || 0
      })))
    }
  });
};
</script>

<style scoped>
.index-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 动态背景元素 - 复用ReportView样式 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-circle {
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.06);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.04);
  font-size: 16px;
  animation: heartbeat 5s ease-in-out infinite;
}

/* 动画效果 - 复用ReportView关键帧 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-12px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.06; }
  50% { transform: scale(1.2); opacity: 0.12; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.05); }
  50% { transform: scale(1); }
  75% { transform: scale(1.02); }
}

/* 页面内容样式 - 与ReportView保持一致 */
.header-section, .filter-section, .list-section {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

.header-card, .filter-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.medical-icon {
  font-size: 36px;
  margin-bottom: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.page-title {
  color: #2c5aa0;
  font-size: 22px;
  font-weight: 600;
  margin: 8px 0;
}

.page-subtitle {
  color: #6b7280;
  font-size: 12px;
  margin: 0;
  font-weight: 300;
}

/* 指标列表样式 */
.index-item {
  background: rgba(255, 255, 255, 0.95);
  margin-bottom: 15px;
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: left;
}

.index-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.index-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c5aa0;
}

.index-desc {
  font-size: 13px;
  color: #6b7280;
  margin-top: 4px;
}

.arrow-icon {
  color: #8b9dc3;
  font-size: 18px;
}

/* 滑动单元格样式 */
.swipe-button {
  height: 100%;
  width: 80px;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 确保列表项样式适配滑动单元格 */
:deep(.van-swipe-cell) {
  margin-bottom: 15px;
  border-radius: 12px;
  overflow: hidden;
}

.index-item {
  margin-bottom: 0; /* 移除原来的底部间距，因为现在由swipe-cell控制 */
}

/* 响应式设计 */
@media (max-width: 480px) {
  .index-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .medical-icon {
    font-size: 32px;
  }
}

.list-section {
  position: relative;
  z-index: 2;
  height: calc(100vh - 100px); /* 调整为适合您页面的高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增加iOS滚动流畅度 */
}

/* 对比模式控制按钮样式 */
.compare-mode-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 0 16px;
}

.compare-toggle-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #2c5aa0;
  border: 1px solid rgba(44, 90, 160, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.compare-toggle-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.start-compare-btn {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 淡入淡出动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 指标项样式优化 */
.index-item {
  display: flex;
  align-items: left;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.index-item.selected-item {
  background: rgba(255, 255, 255, 0.98);
  border-left: 3px solid #667eea;
  box-shadow: 0 6px 15px rgba(102, 126, 234, 0.2);
}

.item-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.checkbox-container {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.index-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
