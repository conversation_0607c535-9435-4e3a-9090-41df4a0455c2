import request from './request'

// 获取医疗检查报告列表
export const getMedicalCheckList = (params) => {
  return request({
    url: '/medical_check',
    method: 'get',
    params
  })
}

// 获取医疗检查详细信息
export const getMedicalCheckDetail = (medical_id) => {
  return request({
    url: '/medical_detail_check',
    method: 'post',
    data: {
      medical_id
    }
  })
}

// 根据条件筛选医疗检查详情
export const getMedicalCheckDetailByFilter = (params) => {
  return request({
    url: '/medical_detail_check',
    method: 'post',
    data: params
  })
}