import axios from 'axios'

// 动态判断环境和基地址
const getBaseURL = () => {
  if (import.meta.env.DEV) {
    // 开发环境使用代理
    return '/api'
  } else {
    // 生产环境判断访问方式
    if (window.location.hostname === 'znick.tpddns.cn') {
      // 通过域名访问时，需要指向实际的后端地址
      return 'http://znick.tpddns.cn:60013' // 或者配置内网穿透
    } else {
      // 本地访问
      return 'http://127.0.0.1:5000'
    }
  }
}

const service = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    config.headers['Authorization'] = localStorage.getItem('token') || ''
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    return Promise.reject(error)
  }
)

export default service