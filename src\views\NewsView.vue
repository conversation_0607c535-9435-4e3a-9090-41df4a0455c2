<template>
  <div class="news-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 资讯内容 -->
    <div class="content-section">
      <div class="content-card">
        <h1 class="page-title">健康资讯</h1>
        <p class="page-subtitle">了解最新医疗健康信息</p>
        
        <!-- 资讯分类标签 -->
        <van-tabs v-model:active="activeTab" sticky>
          <van-tab title="前沿研究">
            <div class="news-list" v-if="frontierNews.length">
              <div v-for="(item, index) in frontierNews" :key="index" class="news-item">
                <h3>{{ item.title }}</h3>
                <p class="news-source">{{ item.source }} · {{ item.date }}</p>
                <p class="news-summary">{{ item.summary }}</p>
                <a :href="item.url" target="_blank" class="read-more">阅读更多</a>
              </div>
            </div>
            <div v-else class="loading-state">加载中...</div>
          </van-tab>
          
          <van-tab title="护理指南">
            <div class="guide-section">
              <!-- 静态内容区域 -->
              <h3>胰腺癌患者日常护理</h3>
              <p>这里是胰腺癌患者的日常护理建议...</p>
              
              <h3>饮食建议</h3>
              <p>胰腺癌患者的饮食注意事项...</p>
              
              <!-- 更多静态内容 -->
            </div>
          </van-tab>
          
          <van-tab title="临床试验">
            <div class="trials-container">
              <div class="trials-header">
                <h3>中国临床试验注册中心</h3>
              </div>
              <div class="trials-actions">
                <van-button 
                  type="primary" 
                  icon="external-link-o" 
                  size="large" 
                  block
                  @click="openTrialsWebsite"
                >
                  访问中国临床试验注册中心
                </van-button>
              </div>
              <div class="trials-info">
                <h4>您可以在中国临床试验注册中心：</h4>
                <ul>
                  <li>查询最新的胰腺癌相关临床试验</li>
                  <li>了解试验招募条件和联系方式</li>
                  <li>获取试验进展和结果信息</li>
                </ul>
              </div>
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { showToast } from 'vant';

// 活动标签
const activeTab = ref(0);

// 前沿研究新闻数据
const frontierNews = ref([]);

// 打开临床试验网站
const openTrialsWebsite = () => {
  window.open('https://www.chictr.org.cn/searchproj.html?title=%E8%83%B0%E8%85%BA%E7%99%8C&officialname=&subjectid=&regstatus=&regno=&secondaryid=&applier=&studyleader=&createyear=&sponsor=&secsponsor=&sourceofspends=&studyailment=&studyailmentcode=&studytype=1&studystage=&studydesign=&recruitmentstatus=&gender=&agreetosign=&measure=&country=&province=&city=&institution=&institutionlevel=&intercode=&ethicalcommitteesanction=&whetherpublic=&minstudyexecutetime=&maxstudyexecutetime=&btngo=btn', '_blank');
};

// 模拟获取新闻数据
const fetchNews = async () => {
  try {
    // 这里替换为实际的API调用
    // const response = await getNewsData();
    
    // 模拟数据
    frontierNews.value = [
      {
        title: "新型胰腺癌免疫疗法研究取得突破",
        source: "医学前沿网",
        date: "2025-06-20",
        summary: "研究人员发现一种新的免疫疗法组合可能对晚期胰腺癌患者有效...",
        url: "#"
      },
      {
        title: "胰腺癌早期检测新方法研究进展",
        source: "健康时报",
        date: "2025-06-18",
        summary: "科学家开发出一种新的血液检测方法，可能帮助早期发现胰腺癌...",
        url: "#"
      }
      // 更多新闻项...
    ];
  } catch (error) {
    console.error('获取新闻失败:', error);
    showToast('获取新闻数据失败');
  }
};

onMounted(() => {
  fetchNews();
});
</script>

<style scoped>
.news-container {
  min-height: calc(100vh - 50px); /* 减去底部导航栏高度 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
  padding-bottom: 80px; /* 增加底部空间，确保不被导航栏遮挡 */
  box-sizing: border-box;
}

/* 动态背景元素样式 - 与HomeView保持一致 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.08);
  font-size: 20px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 5%; left: 8%; }
.floating-cross:nth-child(2) { top: 15%; right: 12%; }
.floating-cross:nth-child(3) { top: 45%; left: 3%; }
.floating-cross:nth-child(4) { top: 65%; right: 8%; }
.floating-cross:nth-child(5) { top: 25%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 35%; left: 15%; }
.floating-cross:nth-child(7) { bottom: 15%; right: 20%; }
.floating-cross:nth-child(8) { top: 80%; left: 70%; }

.floating-circle {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 12%; left: 50%; }
.floating-circle:nth-child(10) { top: 55%; right: 25%; }
.floating-circle:nth-child(11) { bottom: 25%; left: 60%; }
.floating-circle:nth-child(12) { top: 35%; right: 5%; }
.floating-circle:nth-child(13) { bottom: 45%; left: 35%; }
.floating-circle:nth-child(14) { top: 75%; right: 45%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  animation: heartbeat 4s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 30%; left: 20%; }
.floating-heart:nth-child(16) { top: 60%; right: 15%; }
.floating-heart:nth-child(17) { bottom: 20%; left: 80%; }
.floating-heart:nth-child(18) { top: 85%; left: 40%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.08; }
  50% { transform: scale(1.3); opacity: 0.15; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
}

/* 内容区域 */
.content-section {
  position: relative;
  z-index: 2;
  margin-top: 20px;
}

.content-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.page-title {
  color: #2c5aa0;
  font-size: 20px;
  font-weight: 600;
  margin: 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 10px 0;
}

/* 新增样式 */
.news-list {
  margin-top: 15px;
}

.news-item {
  background: white;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.news-item h3 {
  color: #2c5aa0;
  font-size: 16px;
  margin: 0 0 8px 0;
}

.news-source {
  color: #6b7280;
  font-size: 12px;
  margin: 0 0 10px 0;
}

.news-summary {
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
}

.read-more {
  color: #4f46e5;
  font-size: 14px;
  text-decoration: none;
  display: inline-block;
}

.guide-section {
  padding: 15px;
  text-align: left;
}

.guide-section h3 {
  color: #2c5aa0;
  font-size: 16px;
  margin: 15px 0 10px 0;
}

.guide-section p {
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.trials-container {
  padding: 15px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(100, 100, 100, 0.1);
  margin: 15px 0;
}

.trials-header {
  text-align: center;
}

.trials-header h3 {
  color: #2c5aa0;
  font-size: 18px;
  margin-bottom: 8px;
}

.trials-header p {
  color: #666;
  font-size: 14px;
}

.trials-actions {
  margin: 20px 0;
}

.trials-info {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.trials-info h4 {
  color: #333;
  font-size: 15px;
  margin-bottom: 8px;
}

.trials-info ul {
  padding-left: 20px;
  margin: 0;
}

.trials-info li {
  color: #555;
  font-size: 13px;
  margin-bottom: 6px;
  line-height: 1.4;
}
</style>





