const express = require('express');
const router = express.Router();
const axios = require('axios');
const crypto = require('crypto');

// 微信配置
const wxConfig = {
  appId: 'YOUR_APPID', // 替换为您的微信公众号AppID
  appSecret: 'YOUR_APPSECRET', // 替换为您的微信公众号AppSecret
};

// 缓存access_token
let accessToken = '';
let tokenExpireTime = 0;

// 获取access_token
async function getAccessToken() {
  // 如果token未过期，直接返回
  if (accessToken && tokenExpireTime > Date.now()) {
    return accessToken;
  }
  
  try {
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${wxConfig.appId}&secret=${wxConfig.appSecret}`;
    const response = await axios.get(url);
    
    if (response.data && response.data.access_token) {
      accessToken = response.data.access_token;
      // 设置过期时间（提前5分钟过期）
      tokenExpireTime = Date.now() + (response.data.expires_in - 300) * 1000;
      return accessToken;
    } else {
      throw new Error('获取access_token失败: ' + JSON.stringify(response.data));
    }
  } catch (error) {
    console.error('获取access_token出错:', error);
    throw error;
  }
}

// 获取jsapi_ticket
async function getJsApiTicket() {
  try {
    const token = await getAccessToken();
    const url = `https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${token}&type=jsapi`;
    const response = await axios.get(url);
    
    if (response.data && response.data.ticket) {
      return response.data.ticket;
    } else {
      throw new Error('获取jsapi_ticket失败: ' + JSON.stringify(response.data));
    }
  } catch (error) {
    console.error('获取jsapi_ticket出错:', error);
    throw error;
  }
}

// 生成签名
function createSignature(ticket, url, noncestr, timestamp) {
  const str = `jsapi_ticket=${ticket}&noncestr=${noncestr}&timestamp=${timestamp}&url=${url}`;
  return crypto.createHash('sha1').update(str).digest('hex');
}

// 获取JSSDK配置接口
router.get('/jsconfig', async (req, res) => {
  try {
    const url = req.query.url || '';
    if (!url) {
      return res.json({ status: 'error', message: 'URL参数不能为空' });
    }
    
    const ticket = await getJsApiTicket();
    const nonceStr = Math.random().toString(36).substr(2, 15);
    const timestamp = parseInt(Date.now() / 1000);
    const signature = createSignature(ticket, url, nonceStr, timestamp);
    
    res.json({
      status: 'success',
      data: {
        appId: wxConfig.appId,
        timestamp: timestamp,
        nonceStr: nonceStr,
        signature: signature
      }
    });
  } catch (error) {
    console.error('生成JSSDK配置出错:', error);
    res.json({ status: 'error', message: error.message || '服务器内部错误' });
  }
});

module.exports = router;