{"name": "vite-vue-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^0.24.0", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "echarts": "5.6.0", "vue": "^3.5.13", "vue-router": "^4.4.0", "vuex": "^4.0.0"}, "devDependencies": {"@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.3", "amfe-flexible": "^2.2.1", "postcss-pxtorem": "^6.1.0", "unplugin-vue-components": "^28.7.0", "vant": "^4.9.20", "vite": "^6.3.5"}}