<template>
  <van-nav-bar
    :title="title"
    left-text="返回"
    left-arrow
    @click-left="goBack"
    fixed
    placeholder
    :safe-area-inset-top="true"
  />
</template>

<script setup>
import { useRouter } from 'vue-router';

const props = defineProps({
  title: {
    type: String,
    default: ''
  }
});

const router = useRouter();

const goBack = () => {
  router.back();
};
</script>

<style scoped>
:deep(.van-nav-bar) {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 10;
}

:deep(.van-nav-bar__title) {
  color: #2c5aa0;
  font-weight: 600;
  font-size: 16px;
}

:deep(.van-nav-bar__text) {
  color: #2c5aa0;
}

:deep(.van-icon-arrow-left) {
  color: #2c5aa0;
}
</style>


