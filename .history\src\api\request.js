import axios from 'axios'
import store from '../store'
import router from '../router' // Import router

// 动态判断环境和基地址
const getBaseURL = () => {
  if (import.meta.env.DEV) {
    // 开发环境使用代理
    return '/api'
  } else {
    // 生产环境判断访问方式
    if (window.location.hostname === 'znick.tpddns.cn') {
      // 通过域名访问时，需要指向实际的后端地址
      return 'http://znick.tpddns.cn:60013' // 或者配置内网穿透
    } else {
      // 本地访问
      return 'http://127.0.0.1:5000'
    }
  }
}

const service = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在请求拦截器中添加：
    config.headers.Authorization = `Bearer ${store.state.token}`
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 检查响应数据是否包含无效token的错误信息
    const data = response.data
    if (data && data.status === 'error' && data.message === 'Invalid token') {
      // 清除认证信息
      store.commit('CLEAR_AUTH')
      // 跳转到登录页
      router.push('/login')
      // 可以使用Toast提示，但需要导入相关组件
      // 这里简单使用alert
    }
    
    // 返回原始响应
    return response
  },
  error => {
    // 处理响应错误
    if (error.response && error.response.data) {
      const data = error.response.data
      if (data.status === 'error' && data.message === 'Invalid token') {
        // 清除认证信息
        store.commit('CLEAR_AUTH')
        // 跳转到登录页
        router.push('/login')
        // 提示用户
        alert('登录已过期，请重新登录')
      }
    }
    return Promise.reject(error)
  }
)

export default service
