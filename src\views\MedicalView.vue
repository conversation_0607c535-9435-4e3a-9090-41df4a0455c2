<template>
  <div class="medical-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <div class="content-section">
      <div class="content-card">
        <h1 class="page-title">病情管理</h1>
        <p class="page-subtitle">查看最新病情</p>
      </div>
    </div>

    <!-- 功能按钮区域 -->
    <div class="function-section">
      <div class="function-grid">
        <!-- <div class="function-card" @click="navigateTo('/upload')">
          <div class="function-icon upload-icon">📤</div>
          <h3>上传报告</h3>
          <p>上传医疗检查报告</p>
        </div> -->
        
        <div class="function-card" @click="navigateTo('/query-report')">
          <div class="function-icon query-icon">🔍</div>
          <h3>检验报告</h3>
          <p>查看历史检验报告</p>
        </div>

        <div class="function-card" @click="navigateTo('/exam-report')">
          <div class="function-icon exam-icon">🧪</div>
          <h3>检查报告</h3>
          <p>查看检查报告详情</p>
        </div>

        <div class="function-card" @click="navigateTo('/query-indicators')">
          <div class="function-icon indicator-icon">📊</div>
          <h3>查询指标</h3>
          <p>查看指标数据</p>
        </div>

        <div class="function-card" @click="navigateTo('/pathology-report')">
          <div class="function-icon pathology-icon">🔬</div>
          <h3>病理报告</h3>
          <p>查看病理检查报告</p>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { showToast } from 'vant';
import { useRouter } from 'vue-router';

const router = useRouter();

// 导航到指定页面
const navigateTo = (path) => {
  if (path === '/query-report') {
    router.push('/reportView');
  } else if (path === '/exam-report') {
    router.push('/examReportView');
  } else if (path === '/query-indicators') {
    router.push('/indexView');
  } else if (path === '/pathology-report') {
    router.push('/pathologyReportView');
  } else if (path === '/upload') {
    router.push('/upReport');
  } else {
    showToast('功能开发中...');
  }
};


</script>

<style scoped>
.medical-container {
  min-height: calc(100vh - 50px); /* 减去底部导航栏高度 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
  padding-bottom: 80px; /* 增加底部空间，确保不被导航栏遮挡 */
  box-sizing: border-box;
  padding-bottom: 60px; /* 为底部导航栏留出空间 */
}

/* 动态背景元素样式 - 与HomeView保持一致 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.08);
  font-size: 20px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 5%; left: 8%; }
.floating-cross:nth-child(2) { top: 15%; right: 12%; }
.floating-cross:nth-child(3) { top: 45%; left: 3%; }
.floating-cross:nth-child(4) { top: 65%; right: 8%; }
.floating-cross:nth-child(5) { top: 25%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 35%; left: 15%; }
.floating-cross:nth-child(7) { bottom: 15%; right: 20%; }
.floating-cross:nth-child(8) { top: 80%; left: 70%; }

.floating-circle {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 12%; left: 50%; }
.floating-circle:nth-child(10) { top: 55%; right: 25%; }
.floating-circle:nth-child(11) { bottom: 25%; left: 60%; }
.floating-circle:nth-child(12) { top: 35%; right: 5%; }
.floating-circle:nth-child(13) { bottom: 45%; left: 35%; }
.floating-circle:nth-child(14) { top: 75%; right: 45%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  animation: heartbeat 4s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 30%; left: 20%; }
.floating-heart:nth-child(16) { top: 60%; right: 15%; }
.floating-heart:nth-child(17) { bottom: 20%; left: 80%; }
.floating-heart:nth-child(18) { top: 85%; left: 40%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.08; }
  50% { transform: scale(1.3); opacity: 0.15; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
}

/* 内容区域 */
.content-section {
  position: relative;
  z-index: 2;
  margin-top: 20px;
}

.content-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.page-title {
  color: #2c5aa0;
  font-size: 20px;
  font-weight: 600;
  margin: 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 10px 0;
}

/* 功能按钮区域 */
.function-section {
  position: relative;
  z-index: 2;
  margin-top: 20px;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.function-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.function-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.function-card h3 {
  color: #2c5aa0;
  font-size: 16px;
  margin: 10px 0 5px;
}

.function-card p {
  color: #6b7280;
  font-size: 13px;
  margin: 0;
}


</style>