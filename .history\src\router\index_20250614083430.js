import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/LoginView.vue')
  },
  {
    path: '/home',
    name: 'home',
    component: () => import('../views/HomeView.vue'),
    meta: {
      title: '首页',
      requireAuth: true
    }
  },
  {
    path: '/reportView',
    name: 'reportView',
    component: () => import('../views/ReportView.vue'),
    meta: {
      title: '报告查看',
      requireAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

router.beforeEach((to) => {
  if (!localStorage.getItem('token') && to.name !== 'login') {
    return '/login'
  }
})

export default router
