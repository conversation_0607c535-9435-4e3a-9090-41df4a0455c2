<template>
  <van-dialog
    v-model:show="showDialog"
    :title="isEditing ? '编辑数据' : '添加数据'"
    confirm-button-text="保存"
    show-cancel-button
    @confirm="saveData"
  >
    <div class="dialog-content">
      <van-form>
        <van-cell-group inset>
          <van-field
            v-model="localFormData.medical_date"
            is-link
            readonly
            name="medical_date"
            label="检查日期"
            placeholder="选择检查日期"
            @click="showDatePicker = true"
          />
          
          <van-field
            v-model="localFormData.index_value"
            type="number"
            name="index_value"
            label="指标值"
            placeholder="请输入指标值"
          />
          
          <van-field
            v-model="localFormData.comment"
            rows="2"
            autosize
            type="textarea"
            name="comment"
            label="备注"
            placeholder="请输入备注信息"
          />
        </van-cell-group>
      </van-form>
    </div>
  </van-dialog>

  <!-- 日期选择器 -->
  <van-popup v-model:show="showDatePicker" position="bottom">
    <van-date-picker
      :min-date="new Date(2000, 0, 1)"
      :max-date="new Date()"
      @confirm="onDateConfirm"
      @cancel="showDatePicker = false"
    />
  </van-popup>
</template>

<script setup>
import { ref, watch } from 'vue';
import { showNotify } from 'vant';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({
      medical_date: '',
      index_value: '',
      comment: ''
    })
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  referenceMax: {
    type: Number,
    default: null
  },
  referenceMin: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'save']);

const showDialog = ref(props.modelValue);
const showDatePicker = ref(false);
const localFormData = ref({ ...props.formData });

// 监听外部传入的显示状态
watch(() => props.modelValue, (newVal) => {
  showDialog.value = newVal;
});

// 监听内部显示状态变化
watch(() => showDialog.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// 监听外部传入的表单数据
watch(() => props.formData, (newVal) => {
  localFormData.value = { ...newVal };
}, { deep: true });

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  if (typeof date === 'string') {
    date = new Date(date);
  }
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 日期确认
const onDateConfirm = (value) => {
  localFormData.value.medical_date = formatDate(value);
  showDatePicker.value = false;
};

// 根据参考值计算状态
const calculateStatus = (value) => {
  const val = parseFloat(value);
  if (props.referenceMax !== null && props.referenceMax !== undefined && val > props.referenceMax) {
    return 'high';
  } else if (props.referenceMin !== null && props.referenceMin !== undefined && val < props.referenceMin) {
    return 'low';
  }
  return 'normal';
};

// 保存数据
const saveData = () => {
  // 表单验证
  if (!localFormData.value.medical_date) {
    showNotify({ type: 'warning', message: '请选择检查日期' });
    return false;
  }
  
  if (!localFormData.value.index_value) {
    showNotify({ type: 'warning', message: '请输入指标值' });
    return false;
  }
  
  // 构建数据对象
  const data = {
    ...localFormData.value,
    index_status: calculateStatus(localFormData.value.index_value)
  };
  
  // 发送保存事件
  emit('save', data);
  return true;
};
</script>

<style scoped>
.dialog-content {
  padding: 16px 0;
}
</style>

