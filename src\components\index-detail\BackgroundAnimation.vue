<template>
  <div class="bg-animation">
    <div
      v-for="i in 8"
      :key="`cross-${i}`"
      class="floating-cross"
      :style="{
        left: Math.random() * 100 + '%',
        top: Math.random() * 100 + '%',
        animationDelay: Math.random() * 8 + 's'
      }"
    >
      ✚
    </div>
    <div
      v-for="i in 6"
      :key="`circle-${i}`"
      class="floating-circle"
      :style="{
        left: Math.random() * 100 + '%',
        top: Math.random() * 100 + '%',
        animationDelay: Math.random() * 6 + 's'
      }"
    ></div>
    <div
      v-for="i in 4"
      :key="`heart-${i}`"
      class="floating-heart"
      :style="{
        left: Math.random() * 100 + '%',
        top: Math.random() * 100 + '%',
        animationDelay: Math.random() * 5 + 's'
      }"
    >
      ♥
    </div>
  </div>
</template>

<script setup>
// No script needed
</script>

<style scoped>
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross, .floating-circle, .floating-heart {
  position: absolute;
  opacity: 0.1;
  color: white;
  font-size: 24px;
  animation: float 15s infinite linear;
}

.floating-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: white;
  animation: float 20s infinite linear;
}

.floating-heart {
  font-size: 18px;
  animation: float 18s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.1;
  }
  25% {
    opacity: 0.3;
  }
  50% {
    transform: translateY(-100px) rotate(180deg);
    opacity: 0.1;
  }
  75% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(0) rotate(360deg);
    opacity: 0.1;
  }
}
</style>
