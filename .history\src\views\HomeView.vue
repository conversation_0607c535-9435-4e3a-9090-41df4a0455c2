<template>
  <div class="home-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-card">
        <div class="medical-icon">⚕️</div>
        <h1 class="welcome-title">欢迎您，{{ userName }}</h1>
        <!-- <p class="welcome-subtitle">Medical Report System</p> -->
        <div class="current-time">{{ currentTime }}</div>
      </div>
    </div>

    <!-- 功能按钮区域 -->
    <div class="function-section">
      <div class="function-grid">
        <div class="function-card" @click="navigateTo('/upload')">
          <div class="function-icon upload-icon">📤</div>
          <h3>上传报告</h3>
          <p>上传医疗检查报告</p>
        </div>
        
        <div class="function-card" @click="navigateTo('/query-report')">
          <div class="function-icon query-icon">🔍</div>
          <h3>查询报告</h3>
          <p>查看历史检查报告</p>
        </div>
        
        <div class="function-card" @click="navigateTo('/query-indicators')">
          <div class="function-icon indicator-icon">📊</div>
          <h3>查询指标</h3>
          <p>查看指标数据</p>
        </div>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <div class="bottom-section">
      <van-button 
        class="logout-btn" 
        @click="logout"
        icon="arrow-left"
        type="default"
        size="small"
      >
        退出登录
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { showToast, showConfirmDialog } from 'vant';
import { useStore } from 'vuex'

const router = useRouter();
const store = useStore();
const currentTime = ref('');
let timeInterval = null;

// 从Vuex获取用户信息
const userName = computed(() => store.getters.userName || '用户');

// 更新当前时间
const updateTime = () => {
  const now = new Date();
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  };
  currentTime.value = now.toLocaleString('zh-CN', options);
};

// 导航到指定页面
const navigateTo = (path) => {
  if (path === '/query-report') {
    router.push('/reportView');
  } else if (path === '/query-indicators') {
    // 更新指标查询导航
    router.push('/indexView');
  } else if (path === '/upload') {
    // 添加上传报告页面导航
    router.push('/upReport');
  } else {
    showToast('功能开发中...');
  }
};

// 退出登录
const logout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？'
    });
    
    store.commit('CLEAR_AUTH')
    router.push('/login');
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 动态背景元素 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.08);
  font-size: 20px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 5%; left: 8%; }
.floating-cross:nth-child(2) { top: 15%; right: 12%; }
.floating-cross:nth-child(3) { top: 45%; left: 3%; }
.floating-cross:nth-child(4) { top: 65%; right: 8%; }
.floating-cross:nth-child(5) { top: 25%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 35%; left: 15%; }
.floating-cross:nth-child(7) { bottom: 15%; right: 20%; }
.floating-cross:nth-child(8) { top: 80%; left: 70%; }

.floating-circle {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 12%; left: 50%; }
.floating-circle:nth-child(10) { top: 55%; right: 25%; }
.floating-circle:nth-child(11) { bottom: 25%; left: 60%; }
.floating-circle:nth-child(12) { top: 35%; right: 5%; }
.floating-circle:nth-child(13) { bottom: 45%; left: 35%; }
.floating-circle:nth-child(14) { top: 75%; right: 45%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  animation: heartbeat 4s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 30%; left: 20%; }
.floating-heart:nth-child(16) { top: 60%; right: 15%; }
.floating-heart:nth-child(17) { bottom: 20%; left: 80%; }
.floating-heart:nth-child(18) { top: 85%; left: 40%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.08; }
  50% { transform: scale(1.3); opacity: 0.15; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
}

/* 欢迎区域 */
.welcome-section {
  position: relative;
  z-index: 2;
  margin-bottom: 25px;
}

.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.medical-icon {
  font-size: 32px;
  margin-bottom: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.welcome-title {
  color: #2c5aa0;
  font-size: 20px;
  font-weight: 600;
  margin: 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  color: #6b7280;
  font-size: 13px;
  margin: 3px 0 10px 0;
  font-weight: 300;
}

.current-time {
  color: #8b9dc3;
  font-size: 11px;
  font-family: monospace;
}

/* 功能区域 */
.function-section {
  position: relative;
  z-index: 2;
  margin-bottom: 40px;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.function-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 30px 20px;
  border-radius: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.function-icon {
  font-size: 36px;
  margin-bottom: 15px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.upload-icon { color: #10b981; }
.query-icon { color: #3b82f6; }
.indicator-icon { color: #f59e0b; }

.function-card h3 {
  color: #2c5aa0;
  font-size: 18px;
  font-weight: 600;
  margin: 10px 0;
}

.function-card p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

/* 底部区域 */
.bottom-section {
  position: relative;
  z-index: 2;
  text-align: center;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(44, 90, 160, 0.2);
  color: #2c5aa0;
  border-radius: 20px;
  padding: 8px 20px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(44, 90, 160, 0.1);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    padding: 15px;
  }
  
  .function-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .welcome-card {
    padding: 18px 15px;
  }
  
  .welcome-title {
    font-size: 18px;
  }
  
  .medical-icon {
    font-size: 28px;
  }
  
  .function-card {
    padding: 25px 15px;
  }
  
  .function-icon {
    font-size: 32px;
  }
}

@media (max-width: 480px) {
  .welcome-card {
    padding: 15px 12px;
  }
  
  .welcome-title {
    font-size: 16px;
  }
  
  .medical-icon {
    font-size: 26px;
  }
  
  .function-card {
    padding: 20px 15px;
  }
}
</style>
