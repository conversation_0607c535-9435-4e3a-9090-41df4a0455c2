<template>
  <div class="chart-section">
    <div class="chart-card">
      <div class="chart-header">
        <h3>{{ compareMode ? '指标对比分析' : `${indexName} - 趋势分析` }}</h3>
        <van-icon name="share-o" class="share-icon" @click="showShare = true" />
      </div>
      <div class="chart-content">
        <van-loading v-if="loading" size="24px" vertical>加载中...</van-loading>
        <div v-else-if="!compareMode && tableData.length === 0" class="empty-data">
          <van-empty description="暂无数据" />
        </div>
        <div v-else-if="compareMode && Object.keys(multiIndexData).length === 0" class="empty-data">
          <van-empty description="暂无对比数据" />
        </div>
        <div v-else ref="chartContainer" class="chart-container"></div>
      </div>
      
      <!-- 添加图表类型选择器 -->
      <!-- <div v-if="!compareMode && tableData.length > 0" class="chart-type-selector">
        <span class="selector-label">图表类型:</span>
        <div class="selector-options">
          <span :class="['option', {active: chartType === 'line'}]" @click="setChartType('line')">折线图</span>
          <span :class="['option', {active: chartType === 'bar'}]" @click="setChartType('bar')">柱状图</span>
          <span :class="['option', {active: chartType === 'scatter'}]" @click="setChartType('scatter')">散点图</span>
        </div>
      </div> -->
      
      <!-- 添加第二个图表 -->
      <div v-if="!compareMode && tableData.length > 0" class="secondary-chart-section">
        <div class="chart-header">
          <h3>变化率分析</h3>
          <van-icon name="share-o" class="share-icon" @click="showShare = true" />
        </div>
        <div class="chart-content">
          <div ref="secondaryChartContainer" class="secondary-chart-container"></div>
        </div>
      </div>
      
      <!-- 移除数据统计摘要 -->
    </div>
  </div>
  
  <!-- Add ShareSheet component -->
  <van-share-sheet
    v-model:show="showShare"
    title="分享到"
    :options="shareOptions"
    @select="onShareSelect"
  />
</template>

<script setup>
import { ref, onMounted, watch, nextTick, computed, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { showToast } from 'vant';
import { initWxJsSdk } from '@/api/wechat';

// 添加微信JSSDK初始化
onMounted(() => {
  // 初始化微信JSSDK
  if (isWechatBrowser()) {
    initWxJsSdk(() => {
      // JSSDK初始化成功后，预设默认分享内容
      setDefaultWxShare();
    });
  }
});

// 判断是否在微信浏览器中
const isWechatBrowser = () => {
  const ua = navigator.userAgent.toLowerCase();
  return ua.indexOf('micromessenger') !== -1;
};

// 设置默认的微信分享内容
const setDefaultWxShare = () => {
  if (typeof wx === 'undefined') return;
  
  const shareData = {
    title: `${props.indexName}趋势分析`,
    desc: `查看我的${props.indexName}趋势分析数据`,
    link: window.location.href,
    imgUrl: 'https://your-domain.com/logo.png', // 替换为您的应用logo
    success: () => {
      console.log('分享设置成功');
    }
  };
  
  // 设置分享给朋友
  wx.updateAppMessageShareData(shareData);
  
  // 设置分享到朋友圈
  wx.updateTimelineShareData({
    title: shareData.title,
    link: shareData.link,
    imgUrl: shareData.imgUrl,
    success: shareData.success
  });
};

const props = defineProps({
  indexName: {
    type: String,
    required: true
  },
  tableData: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  referenceMax: {
    type: Number,
    default: null
  },
  referenceMin: {
    type: Number,
    default: null
  },
  compareMode: {
    type: Boolean,
    default: false
  },
  multiIndexData: {
    type: Object,
    default: () => ({})
  },
  indexesList: {
    type: Array,
    default: () => ([])
  }
});

const chartContainer = ref(null);
let chartInstance = null;

// Add these new refs
const showShare = ref(false);
const shareOptions = [
  { name: '微信', icon: 'wechat', color: '#07c160' },
  { name: '朋友圈', icon: 'friends-o', color: '#07c160' },
  { name: '复制链接', icon: 'link', color: '#1989fa' },
  { name: '分享图片', icon: 'photograph', color: '#ee0a24' }
];

// Add share handler function
const onShareSelect = async (option) => {
  // 准备分享内容
  const shareTitle = `${props.indexName}趋势分析`;
  const shareDesc = `查看我的${props.indexName}趋势分析数据`;
  const shareUrl = window.location.href;
  const shareImgUrl = chartInstance ? chartInstance.getDataURL({
    backgroundColor: '#ffffff',
    pixelRatio: 2,
    excludeComponents: ['toolbox']
  }) : '';
  
  try {
    switch(option.name) {
      case '微信':
        // await shareToWeChat(shareTitle, shareDesc, shareUrl, shareImgUrl);
        break;
      case '朋友圈':
        // await shareToWeChatMoments(shareTitle, shareDesc, shareUrl, shareImgUrl);
        break;
      case '复制链接':
        await copyShareLink(shareUrl);
        break;
      case '分享图片':
        downloadChartImage();
        break;
      default:
        showToast(`已选择 ${option.name}`);
    }
  } catch (error) {
    console.error('分享失败:', error);
    showToast('分享失败，请稍后重试');
  }
  
  showShare.value = false;
};

// 微信分享函数更新
const shareToWeChat = async (title, desc, url, imgUrl) => {
  // 检查是否在微信环境中
  if (typeof wx !== 'undefined') {
    try {
      wx.ready(() => {
        wx.updateAppMessageShareData({
          title: title,
          desc: desc,
          link: url,
          imgUrl: imgUrl || 'https://your-domain.com/logo.png', // 使用默认图片
          success: () => {
            showToast('分享设置成功，请点击右上角"..."分享');
          }
        });
      });
    } catch (error) {
      console.error('微信分享设置失败:', error);
      showToast('微信分享设置失败');
    }
  } else {
    showToast('请在微信中打开进行分享');
  }
};

// 微信朋友圈分享函数更新
const shareToWeChatMoments = async (title, desc, url, imgUrl) => {
  if (typeof wx !== 'undefined') {
    try {
      wx.ready(() => {
        wx.updateTimelineShareData({
          title: title,
          link: url,
          imgUrl: imgUrl || 'https://your-domain.com/logo.png', // 使用默认图片
          success: () => {
            showToast('分享设置成功，请点击右上角"..."分享到朋友圈');
          }
        });
      });
    } catch (error) {
      console.error('微信朋友圈分享设置失败:', error);
      showToast('微信朋友圈分享设置失败');
    }
  } else {
    showToast('请在微信中打开进行分享');
  }
};

// 复制链接函数
const copyShareLink = async (url) => {
  try {
    await navigator.clipboard.writeText(url);
    showToast('链接已复制');
  } catch (error) {
    console.error('复制失败:', error);
    showToast('复制失败，请手动复制');
    
    // 备用复制方法
    const input = document.createElement('input');
    input.value = url;
    document.body.appendChild(input);
    input.select();
    document.execCommand('copy');
    document.body.removeChild(input);
  }
};

// 下载图表图片
const downloadChartImage = () => {
  if (chartInstance) {
    // 设置导出选项，确保背景色为白色且清晰
    const url = chartInstance.getDataURL({
      backgroundColor: '#ffffff',
      pixelRatio: 2, // 提高分辨率
      excludeComponents: ['toolbox'] // 排除工具箱等UI组件
    });
    
    const link = document.createElement('a');
    link.download = `${props.indexName}趋势图.png`;
    link.href = url;
    link.click();
    showToast('图片已下载');
  } else {
    showToast('图表未加载，无法下载');
  }
};

// 生成二维码
const generateQRCode = (url) => {
  // 这里可以使用QRCode.js等库生成二维码
  // 简化实现，实际应用中需要引入QRCode库
  showToast('二维码生成功能开发中');
  
  // 示例代码，需要先引入QRCode库
  /*
  if (typeof QRCode !== 'undefined') {
    // 创建一个临时容器
    const qrContainer = document.createElement('div');
    document.body.appendChild(qrContainer);
    
    new QRCode(qrContainer, {
      text: url,
      width: 256,
      height: 256
    });
    
    // 获取生成的二维码图片
    setTimeout(() => {
      const qrImg = qrContainer.querySelector('img');
      if (qrImg) {
        const link = document.createElement('a');
        link.download = `${props.indexName}二维码.png`;
        link.href = qrImg.src;
        link.click();
      }
      document.body.removeChild(qrContainer);
    }, 100);
  }
  */
};

// 添加新的响应式变量
const chartType = ref('line');
const secondaryChartContainer = ref(null);
let secondaryChartInstance = null;

// 设置图表类型的方法
const setChartType = (type) => {
  chartType.value = type;
  nextTick(() => {
    initChart();
  });
};

// 监听数据变化，重新渲染图表
watch(() => props.tableData, () => {
  if (props.tableData.length > 0) {
    nextTick(() => {
      initChart();
    });
  }
}, { deep: true });

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'high': return '偏高';
    case 'low': return '偏低';
    default: return '正常';
  }
};

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;
  if (!props.compareMode && props.tableData.length === 0) return;
  if (props.compareMode && Object.keys(props.multiIndexData).length === 0) return;

  // 销毁已存在的图表实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartContainer.value);

  // 准备基础配置
  const option = {
    title: {
      text: props.compareMode ? '指标对比图' : `${props.indexName} 趋势图`,
      left: 'center',
      textStyle: {
        color: '#2c5aa0',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      enterable: true,
      position: function(pos, params, dom, rect, size) {
        const obj = {top: 10};
        obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30;
        return obj;
      },
      formatter: function(params) {
        if (props.compareMode) {
          // 对比模式的tooltip格式
          let result = `<div style="margin-bottom: 5px; font-weight: bold;">${params[0].axisValue}</div>`;
          params.forEach(param => {
            result += `<div style="margin: 2px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.value}
            </div>`;
          });
          return result;
        } else {
          // 单指标模式的tooltip格式，包含备注信息
          const param = params[0];
          const dataIndex = param.dataIndex;
          // 获取排序后的数据
          const sortedData = [...props.tableData].sort((a, b) => new Date(a.medical_date) - new Date(b.medical_date));
          const dataItem = sortedData[dataIndex];

          let result = `<div style="margin-bottom: 5px; font-weight: bold;">${param.axisValue}</div>`;
          result += `<div style="margin: 2px 0;">
            <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
            ${param.seriesName}: ${param.value}
          </div>`;

          // 添加备注信息
          if (dataItem && dataItem.comment) {
            const comment = dataItem.comment.length > 10
              ? dataItem.comment.substring(0, 10) + '...'
              : dataItem.comment;
            result += `<div style="margin-top: 5px; padding-top: 5px; border-top: 1px solid #eee; color: #666; font-size: 12px;">
              备注: ${comment}
            </div>`;
          }

          return result;
        }
      }
    },
    legend: {
      data: props.compareMode ? props.indexesList.map(item => item.index_name) : [props.indexName],
      right: 10,
      top: 30,
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    grid: {
      left: '10%',
      right: props.compareMode ? '10%' : '3%',
      bottom: '20%',
      top: '20%'
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: props.compareMode ? [
      {
        type: 'value',
        scale: true,
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        scale: true,
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ] : {
      type: 'value',
      scale: false, // 改为false以确保显示完整范围
      axisLabel: {
        formatter: '{value}'
      },
      // 计算Y轴范围，确保包含正常范围
      min: function(value) {
        if (props.referenceMin !== null && props.referenceMin !== undefined) {
          const dataMin = value.min;
          const refMin = props.referenceMin;
          const minValue = Math.min(dataMin, refMin);
          // 留出一些边距
          return minValue - (Math.abs(minValue) * 0.1);
        }
        return value.min;
      },
      max: function(value) {
        if (props.referenceMax !== null && props.referenceMax !== undefined) {
          const dataMax = value.max;
          const refMax = props.referenceMax;
          const maxValue = Math.max(dataMax, refMax);
          // 留出一些边距
          return maxValue + (Math.abs(maxValue) * 0.1);
        }
        return value.max;
      }
    },
    series: []
  };

  // 根据模式设置不同的数据
  if (props.compareMode) {
    // 对比模式：多指标数据
    const allDates = new Set();
    const colorMap = ['#667eea', '#f6ad55', '#48bb78', '#ed64a6'];
    
    // 收集所有日期
    Object.values(props.multiIndexData).forEach(dataArray => {
      dataArray.forEach(item => {
        allDates.add(item.medical_date);
      });
    });
    
    // 排序日期
    const sortedDates = Array.from(allDates).sort((a, b) => new Date(a) - new Date(b));
    option.xAxis.data = sortedDates.map(date => formatDate(date));
    
    // 为每个指标创建系列
    Object.entries(props.multiIndexData).forEach(([indexName, dataArray], idx) => {
      const seriesData = [];
      
      // 为每个日期找到对应的数据点
      sortedDates.forEach(date => {
        const dataPoint = dataArray.find(item => item.medical_date === date);
        seriesData.push(dataPoint ? parseFloat(dataPoint.index_value) : null);
      });
      
      // 添加系列
      option.series.push({
        name: indexName,
        type: 'line',
        data: seriesData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        yAxisIndex: idx % 2, // 交替使用左右轴
        lineStyle: {
          color: colorMap[idx % colorMap.length],
          width: 2
        },
        itemStyle: {
          color: colorMap[idx % colorMap.length]
        }
      });
    });
  } else {
    // 单指标模式：根据选择的图表类型设置
    const sortedData = [...props.tableData].sort((a, b) => new Date(a.medical_date) - new Date(b.medical_date));
    const dates = sortedData.map(item => formatDate(item.medical_date));
    const values = sortedData.map(item => parseFloat(item.index_value) || 0);

    option.xAxis.data = dates;
    option.series = [{
      name: props.indexName,
      type: chartType.value, // 使用选择的图表类型
      data: values.map(v => parseFloat(v.toFixed(2))),
      smooth: chartType.value === 'line', // 只有折线图才平滑
      symbol: chartType.value === 'line' || chartType.value === 'scatter' ? 'circle' : 'none',
      symbolSize: chartType.value === 'scatter' ? 10 : 6,
      lineStyle: {
        color: '#667eea',
        width: 2
      },
      itemStyle: {
        color: '#667eea'
      },
      ...(props.referenceMin !== null && props.referenceMin !== undefined && 
          props.referenceMax !== null && props.referenceMax !== undefined ? {
        markArea: {
          silent: true,
          itemStyle: {
            color: 'rgba(34, 197, 94, 0.1)',
            borderColor: 'rgba(34, 197, 94, 0.3)',
            borderWidth: 1,
            borderType: 'dashed'
          },
          label: {
            show: true,
            position: 'insideTopRight',
            formatter: '正常范围',
            color: '#22c55e',
            fontSize: 10
          },
          data: [[
            {
              yAxis: props.referenceMin
            },
            {
              yAxis: props.referenceMax
            }
          ]]
        }
      } : {})
    }];
  }

  chartInstance.setOption(option);
  
  // 初始化第二个图表（变化率分析）
  initSecondaryChart();

  // 响应式调整
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize();
    }
    if (secondaryChartInstance) {
      secondaryChartInstance.resize();
    }
  });
};

// 初始化第二个图表
const initSecondaryChart = () => {
  if (!secondaryChartContainer.value || props.compareMode || props.tableData.length < 2) return;
  
  if (secondaryChartInstance) {
    secondaryChartInstance.dispose();
  }
  
  secondaryChartInstance = echarts.init(secondaryChartContainer.value);
  
  // 计算变化率数据
  const sortedData = [...props.tableData].sort((a, b) => 
    new Date(a.medical_date) - new Date(b.medical_date));
  
  const dates = sortedData.map(item => formatDate(item.medical_date));
  const values = sortedData.map(item => parseFloat(item.index_value) || 0);
  
  // 计算变化率
  const changeRates = [];
  for (let i = 1; i < values.length; i++) {
    const prevValue = values[i-1];
    const currentValue = values[i];
    const changeRate = prevValue !== 0 ? ((currentValue - prevValue) / prevValue * 100) : 0;
    changeRates.push(parseFloat(changeRate.toFixed(2)));
  }
  
  // 变化率图表配置
  const option = {
    title: {
      text: '指标变化率',
      left: 'center',
      textStyle: {
        color: '#2c5aa0',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    legend: {
      data: ['变化率'],
      right: 10,
      top: 0,
      textStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    grid: {
      left: '10%',
      right: '3%',
      bottom: '20%', // 增加底部空间，避免日期遮挡
      top: '20%'     // 增加顶部空间，为图例留出位置
    },
    xAxis: {
      type: 'category',
      data: dates.slice(1), // 从第二个日期开始
      axisLabel: {
        rotate: 45,
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      name: '变化率',
      type: 'bar',
      data: changeRates,
      itemStyle: {
        color: function(params) {
          return params.data >= 0 ? '#f87171' : '#4ade80';
        }
      }
    }]
  };
  
  secondaryChartInstance.setOption(option);
};

// 组件挂载时初始化图表
onMounted(() => {
  if (props.tableData.length > 0) {
    nextTick(() => {
      initChart();
    });
  }
});

// 组件销毁时清理图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  if (secondaryChartInstance) {
    secondaryChartInstance.dispose();
  }
  window.removeEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize();
    }
    if (secondaryChartInstance) {
      secondaryChartInstance.resize();
    }
  });
});

// 添加计算属性和方法
// 获取最新值
// const latestValue = computed(() => {
//   if (!props.tableData || props.tableData.length === 0) return '-';
//   const sortedData = [...props.tableData].sort((a, b) => 
//     new Date(b.medical_date) - new Date(a.medical_date));
//   return parseFloat(sortedData[0].index_value).toFixed(2);
// });

// 计算平均值
// const averageValue = computed(() => {
//   if (!props.tableData || props.tableData.length === 0) return '-';
//   const sum = props.tableData.reduce((acc, item) => acc + parseFloat(item.index_value), 0);
//   return (sum / props.tableData.length).toFixed(2);
// });

// 获取单位
// const getUnit = () => {
//   if (!props.tableData || props.tableData.length === 0) return '';
//   return props.tableData[0].index_unit || '';
// };

// 计算趋势
// const trendText = computed(() => {
//   if (!props.tableData || props.tableData.length < 2) return '数据不足';
  
//   const sortedData = [...props.tableData].sort((a, b) => 
//     new Date(a.medical_date) - new Date(b.medical_date));
  
//   const firstValue = parseFloat(sortedData[0].index_value);
//   const lastValue = parseFloat(sortedData[sortedData.length - 1].index_value);
  
//   const change = ((lastValue - firstValue) / firstValue * 100).toFixed(1);
  
//   if (change > 0) return `上升 ${change}%`;
//   if (change < 0) return `下降 ${Math.abs(change)}%`;
//   return '保持稳定';
// });

// 获取趋势样式类
// const getTrendClass = () => {
//   if (!props.tableData || props.tableData.length < 2) return '';
  
//   const sortedData = [...props.tableData].sort((a, b) => 
//     new Date(a.medical_date) - new Date(b.medical_date));
  
//   const firstValue = parseFloat(sortedData[0].index_value);
//   const lastValue = parseFloat(sortedData[sortedData.length - 1].index_value);
  
//   if (lastValue > firstValue) return 'trend-up';
//   if (lastValue < firstValue) return 'trend-down';
//   return '';
// };
</script>

<style scoped>
.chart-section {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(102, 126, 234, 0.05);
}

.chart-header h3 {
  margin: 0;
  color: #2c5aa0;
  font-size: 18px;
  font-weight: 600;
}

.share-icon {
  font-size: 20px;
  color: #2c5aa0;
  cursor: pointer;
  padding: 8px;
}

.chart-content {
  padding: 20px;
}

.chart-container {
  height: 380px; /* 增加图表高度，给X轴更多空间 */
  width: 100%;
}

.empty-data {
  padding: 30px 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header h3 {
    font-size: 16px;
  }

  .chart-content {
    padding: 15px;
  }

  .chart-container {
    height: 330px; /* 响应式调整 */
  }
}

@media (max-width: 480px) {
  .chart-header {
    padding: 5px 10px;
  }

  .chart-content {
    padding: 10px;
  }

  .chart-container {
    height: 280px; /* 响应式调整 */
  }
}

/* 移除 .chart-summary 相关样式 */

/* 添加图表类型选择器样式 */
.chart-type-selector {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(102, 126, 234, 0.03);
}

.selector-label {
  font-size: 14px;
  color: #6b7280;
  margin-right: 10px;
}

.selector-options {
  display: flex;
  gap: 10px;
}

.option {
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  cursor: pointer;
  background: #f3f4f6;
  color: #4b5563;
  transition: all 0.2s;
}

.option.active {
  background: #667eea;
  color: white;
}

/* 第二个图表样式 */
.secondary-chart-section {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.secondary-chart-header {
  margin: 15px 0 5px;
  padding: 0 20px;
  color: #2c5aa0;
  font-size: 16px;
  font-weight: 500;
}

.secondary-chart-container {
  height: 280px; /* 增加高度，确保有足够空间 */
  margin-top: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .secondary-chart-container {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .secondary-chart-container {
    height: 220px;
  }
}
</style>


























