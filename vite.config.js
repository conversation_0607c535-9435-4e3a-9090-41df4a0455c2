import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
import pxtorem from 'postcss-pxtorem'
import path from 'path'  // 新增path模块引入

export default defineConfig({
  server: {
    host: '0.0.0.0',
    port: 5173,
    allowedHosts: ['33ld558cy557.vicp.fun'],
    open: true,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:5000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  plugins: [
    vue(),
    Components({
      resolvers: [VantResolver({
        importStyle: false, // 使用CSS格式的样式
        resolveIcons: false
      })]
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src') // 新增别名配置
    }
  },
  css: {
    postcss: {
      plugins: [
        pxtorem({ // 改为ESM使用方式
          rootValue: 37.5,
          propList: ['*'],
          selectorBlackList: ['.norem']
        })
      ]
    }
  }
})