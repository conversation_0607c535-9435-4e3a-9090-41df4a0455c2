<template>
  <div class="toggle-section">
    <div class="toggle-buttons">
      <button
        :class="['toggle-btn', { active: modelValue === 'table' }]"
        @click="$emit('update:modelValue', 'table')"
      >
        <van-icon name="list-switch" />
        表格数据
      </button>
      <button
        :class="['toggle-btn', { active: modelValue === 'chart' }]"
        @click="$emit('update:modelValue', 'chart')"
      >
        <van-icon name="chart-trending-o" />
        趋势图表
      </button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  modelValue: {
    type: String,
    required: true
  }
});

defineEmits(['update:modelValue']);
</script>

<style scoped>
.toggle-section {
  margin-bottom: 10px;
  position: relative;
  z-index: 2;
}

.toggle-buttons {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.toggle-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.toggle-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.toggle-btn:hover:not(.active) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toggle-btn {
    font-size: 12px;
    padding: 10px 12px;
  }
}

@media (max-width: 480px) {
  .toggle-buttons {
    padding: 3px;
  }

  .toggle-btn {
    font-size: 11px;
    padding: 8px 10px;
    gap: 4px;
  }
}
</style>
