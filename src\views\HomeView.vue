<template>
  <div class="home-container">
    <!-- 主要内容区域 -->
    <router-view />
    
    <!-- 底部导航栏 -->
    <van-tabbar v-model="activeTab" route fixed>
      <van-tabbar-item to="/home/<USER>" icon="home-o">主页</van-tabbar-item>
      <van-tabbar-item to="/home/<USER>" icon="newspaper-o">资讯</van-tabbar-item>
      <van-tabbar-item to="/home/<USER>" icon="chart-trending-o">病情</van-tabbar-item>
      <van-tabbar-item to="/home/<USER>" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const activeTab = ref(0);

// 根据当前路由设置激活的标签页
const updateActiveTab = () => {
  const path = route.path;
  if (path.includes('/home/<USER>')) {
    activeTab.value = 0;
  } else if (path.includes('/home/<USER>')) {
    activeTab.value = 1;
  } else if (path.includes('/home/<USER>')) {
    activeTab.value = 2;
  } else if (path.includes('/home/<USER>')) {
    activeTab.value = 3;
  }
};

// 监听路由变化
watch(() => route.path, updateActiveTab);

// 组件挂载时设置激活标签
onMounted(() => {
  updateActiveTab();
});
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  position: relative;
  padding-bottom: 50px; /* 为底部导航栏留出空间 */
}

/* 自定义底部导航栏样式 */
:deep(.van-tabbar) {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000; /* 确保导航栏在最上层 */
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

:deep(.van-tabbar-item) {
  color: #8b9dc3;
}

:deep(.van-tabbar-item--active) {
  color: #2c5aa0;
}
</style>
