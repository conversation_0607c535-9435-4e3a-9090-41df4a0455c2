import { createRouter, createWebHashHistory } from 'vue-router'
import store from '../store'
// 导入上传报告页面
import UpReport from '../views/UpReport.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/LoginView.vue')
  },
  {
    path: '/home',
    name: 'home',
    component: () => import('../views/HomeView.vue'),
    meta: {
      title: '首页'
    }
  },
  {
    path: '/reportView',
    name: 'reportView',
    component: () => import('../views/ReportView.vue'),
    meta: {
      title: '报告查看',
      requireAuth: true
    }
  },
  // 新增指标查询路由
  {
    path: '/indexView',
    name: 'indexView',
    component: () => import('../views/IndexView.vue'),
    meta: {
      keepAlive: true, // 标记需要缓存的组件
      title: '指标查询',
      requiresAuth: true
    }
  },
  // 新增指标详情路由
  {
    path: '/indexDetail',
    name: 'indexDetail',
    component: () => import('../views/IndexViewDetail.vue'),
    meta: {
      title: '指标详情',
      requireAuth: true
    }
  },
  {
    path: '/upReport',
    name: 'upReport',
    component:  () => import('../views/UpReport.vue'),
    meta: {
      title: '上传报告', 
      requiresAuth: true 
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 会话超时时间：1小时（3600000毫秒）
const SESSION_TIMEOUT = 3600000;

// 清除自动登出定时器
const clearLogoutTimer = () => {
  if (window.logoutTimer) {
    clearTimeout(window.logoutTimer);
    window.logoutTimer = null;
  }
};

// 设置自动登出定时器
const setLogoutTimer = () => {
  clearLogoutTimer();
  const loginTime = localStorage.getItem('loginTime');
  if (loginTime) {
    const now = Date.now();
    const timeLeft = SESSION_TIMEOUT - (now - parseInt(loginTime));
    if (timeLeft > 0) {
      window.logoutTimer = setTimeout(() => {
        store.commit('CLEAR_AUTH');
        router.push('/login');
      }, timeLeft);
    }
  }
};

router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters.isAuthenticated;
  const loginTime = localStorage.getItem('loginTime');
  const now = Date.now();

  // 检查登录是否过期
  if (isAuthenticated && loginTime) {
    if (now - parseInt(loginTime) > SESSION_TIMEOUT) {
      store.commit('CLEAR_AUTH');
      return next('/login');
    }
    // 设置自动登出定时器
    setLogoutTimer();
  } else {
    // 未登录状态清除定时器
    clearLogoutTimer();
  }

  // 原有路由守卫逻辑
  if (to.meta.requireAuth && !isAuthenticated) {
    next('/login');
  } else if (to.path === '/login' && isAuthenticated) {
    next('/home');
  } else {
    next();
  }
});

export default router





