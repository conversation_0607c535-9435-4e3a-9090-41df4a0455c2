<template>
  <div class="table-section">
    <div class="table-card">
      <div class="table-header">
        <h3>{{ indexName }} - 历史数据</h3>
      </div>
      <div class="table-content">
        <van-loading v-if="loading" size="24px" vertical>加载中...</van-loading>
        <div v-else-if="tableData.length === 0" class="empty-data">
          <van-empty description="暂无数据">
            <van-button 
              round 
              type="primary" 
              class="empty-add-btn" 
              @click="$emit('add')"
            >
              添加第一条数据
            </van-button>
          </van-empty>
        </div>
        <div v-else class="data-table">
          <table>
            <thead>
              <tr>
                <th class="col-date">检查日期</th>
                <th class="col-value">指标值</th>
                <th class="col-status">状态</th>
                <th class="col-comment">备注</th>
                <th class="col-actions">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in tableData" :key="index">
                <td class="col-date">{{ formatDate(item.medical_date) }}</td>
                <td :class="['col-value', getValueClass(item.index_status)]">{{ item.index_value }}</td>
                <td class="col-status">
                  <span :class="['status-tag', getStatusClass(item.index_status)]">
                    {{ getStatusText(item.index_status) }}
                  </span>
                </td>
                <td class="col-comment">
                  <div v-if="item.comment && item.comment.length > 10" class="expandable-comment">
                    <span :class="['comment-text', {'expanded': expandedComments[index]}]">
                      {{ item.comment }}
                    </span>
                    <span class="expand-btn" @click.stop="toggleComment(index)">
                      {{ expandedComments[index] ? '收起' : '展开' }}
                    </span>
                  </div>
                  <div v-else>{{ item.comment || '-' }}</div>
                </td>
                <td class="col-actions">
                  <div class="action-buttons">
                    <van-button 
                      size="mini" 
                      type="primary" 
                      icon="edit" 
                      @click.stop="$emit('edit', item, index)"
                    />
                    <van-button 
                      size="mini" 
                      type="danger" 
                      icon="delete" 
                      @click.stop="$emit('delete', item, index)"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

defineProps({
  indexName: {
    type: String,
    required: true
  },
  tableData: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

defineEmits(['add', 'edit', 'delete']);

const expandedComments = ref({});

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 获取数值样式类
const getValueClass = (status) => {
  switch (status) {
    case 'high': return 'value-high';
    case 'low': return 'value-low';
    default: return 'value-normal';
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'high': return 'status-high';
    case 'low': return 'status-low';
    default: return 'status-normal';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'high': return '偏高';
    case 'low': return '偏低';
    default: return '正常';
  }
};

// 切换备注展开/收起状态
const toggleComment = (index) => {
  expandedComments.value[index] = !expandedComments.value[index];
};
</script>

<style scoped>
.table-section {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

.table-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.table-header {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(102, 126, 234, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  margin: 0;
  color: #2c5aa0;
  font-size: 18px;
  font-weight: 600;
}

.table-content {
  padding: 20px;
}

/* 表格样式 */
.data-table {
  overflow-x: auto;
  width: 100%;
}

.data-table table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  background: rgba(102, 126, 234, 0.1);
  color: #2c5aa0;
  font-weight: 600;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.data-table td {
  padding: 12px 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  color: #374151;
}

.data-table tr:hover {
  background: rgba(102, 126, 234, 0.02);
}

/* 数值样式 */
.value-normal {
  color: #10b981;
  font-weight: 600;
}

.value-high {
  color: #ef4444;
  font-weight: 600;
}

.value-low {
  color: #f59e0b;
  font-weight: 600;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-normal {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-high {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.status-low {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* 备注展开/收起 */
.expandable-comment {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.comment-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  transition: all 0.3s;
}

.comment-text.expanded {
  white-space: normal;
  overflow: visible;
  max-width: none;
}

.expand-btn {
  color: #2c5aa0;
  cursor: pointer;
  font-size: 12px;
  margin-left: 5px;
  white-space: nowrap;
  user-select: none;
  flex-shrink: 0;
}

.expand-btn:hover {
  text-decoration: underline;
}

/* 表格列宽设置 */
.col-date {
  width: 80px;
}

.col-value {
  width: 50px;
}

.col-status {
  width: 45px;
}

.col-comment {
  min-width: 150px;
}

.col-actions {
  width: 80px;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.action-buttons .van-button {
  padding: 0;
  width: 28px;
  height: 28px;
}

/* 空数据状态 */
.empty-data {
  padding: 30px 0;
  text-align: center;
}

.empty-add-btn {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header h3 {
    font-size: 16px;
  }

  .table-content {
    padding: 15px;
  }

  .data-table {
    font-size: 12px;
  }

  .data-table th, .data-table td {
    padding: 8px 6px;
  }

  .col-actions {
    width: 60px;
  }
  
  .action-buttons .van-button {
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .table-header {
    padding: 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table-content {
    padding: 10px;
  }

  .data-table {
    font-size: 11px;
  }

  .data-table th, .data-table td {
    padding: 6px 4px;
  }

  .status-tag {
    font-size: 10px;
    padding: 2px 6px;
  }

  .col-actions {
    width: 50px;
  }
  
  .action-buttons {
    gap: 3px;
  }
  
  .action-buttons .van-button {
    width: 22px;
    height: 22px;
  }
}
</style>
