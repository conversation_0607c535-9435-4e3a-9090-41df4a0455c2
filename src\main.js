import 'amfe-flexible';
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import Vant from 'vant'
import 'vant/lib/index.css'
import * as echarts from 'echarts'

// 创建 Vue 实例
const app = createApp(App)

// 配置 echarts（保持原有使用方式不变）
app.config.globalProperties.$echarts = echarts

// 安装 Vant
app.use(Vant)
app.use(store).use(router)

app.mount('#app')
