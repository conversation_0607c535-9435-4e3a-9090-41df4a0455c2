
<template>
  <div class="index-detail-container">
    <!-- 背景动画 -->
    <background-animation />
    
    <!-- 返回按钮 -->
    <back-button :title="compareMode ? '指标对比' : '指标明细'" />

    <!-- 视图切换按钮 -->
    <view-toggle 
      v-if="showChart && !compareMode" 
      v-model="currentView"
    />

    <!-- 表格视图 -->
    <data-table
      v-if="currentView === 'table' && !compareMode"
      :index-name="indexName"
      :table-data="tableData"
      :loading="loading"
      :is-editable="isEditable"
      @add="openAddDialog"
      @edit="editItem"
      @delete="confirmDelete"
    />

    <!-- 图表视图 -->
    <chart-view
      v-if="(showChart && currentView === 'chart') || compareMode"
      :index-name="indexName"
      :table-data="tableData"
      :loading="loading"
      :reference-max="referenceMax"
      :reference-min="referenceMin"
      :compare-mode="compareMode"
      :multi-index-data="multiIndexData"
      :indexes-list="indexesList"
    />

    <!-- 添加浮动气泡 -->
    <van-floating-bubble 
      v-if="!compareMode && isEditable"
      axis="xy"
      icon="plus"
      magnetic="x"
      @click="openAddDialog"
    />

    <!-- 添加/编辑数据表单 -->
    <data-form
      v-model="showAddDialog"
      :form-data="formData"
      :is-editing="isEditing"
      :reference-max="referenceMax"
      :reference-min="referenceMin"
      :comment-only="!isEditable && isEditing"
      @save="saveData"
    />

    <!-- 删除确认对话框 -->
    <van-dialog
      v-model:show="showDeleteConfirm"
      title="确认删除"
      message="确定要删除这条数据吗？此操作不可恢复。"
      show-cancel-button
      @confirm="deleteData"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
import { showSuccessToast, showFailToast, showToast, showNotify } from 'vant';
import { 
  getMedicalIndexDetail, 
  updateMedicalCheckComment, 
  addMedicalCheckDetail,
  deleteMedicalCheckDetail
} from '@/api/medical';

// 导入拆分的组件
import BackButton from '@/components/index-detail/BackButton.vue';
import BackgroundAnimation from '@/components/index-detail/BackgroundAnimation.vue';
import ViewToggle from '@/components/index-detail/ViewToggle.vue';
import DataTable from '@/components/index-detail/DataTable.vue';
import ChartView from '@/components/index-detail/ChartView.vue';
import DataForm from '@/components/index-detail/DataForm.vue';

const route = useRoute();

// 响应式数据
const loading = ref(false);
const currentView = ref('table');
const tableData = ref([]);
const indexName = ref('');
const indexId = ref('');
const showChart = ref(false);
const referenceMax = ref(null);
const referenceMin = ref(null);
const showAddDialog = ref(false);
const showDeleteConfirm = ref(false);
const isEditing = ref(false);
const currentEditIndex = ref(-1);
const isEditable = ref(false); // 是否可编辑
const formData = ref({
  medical_date: '',
  index_value: '',
  index_unit: '',
  reference_value: '',
  index_status: 'normal',
  comment: ''
});

// 添加对比模式相关状态
const compareMode = ref(false);
const indexesList = ref([]);

// 添加多指标数据存储
const multiIndexData = ref({});

// 获取路由参数
onMounted(() => {
  // 检查是否是对比模式
  compareMode.value = route.query.compare_mode === '1';
  
  if (compareMode.value) {
    try {
      // 解析传入的多个指标信息
      indexesList.value = JSON.parse(route.query.indexes || '[]');
      if (indexesList.value.length > 0) {
        // 设置第一个指标为当前显示的指标
        indexId.value = indexesList.value[0].index_id;
        indexName.value = indexesList.value[0].index_name || '未知指标';
        showChart.value = true; // 对比模式强制显示图表
        referenceMax.value = indexesList.value[0].reference_max ? parseFloat(indexesList.value[0].reference_max) : null;
        referenceMin.value = indexesList.value[0].reference_min ? parseFloat(indexesList.value[0].reference_min) : null;
        isEditable.value = indexesList.value[0].is_edit === '1' || indexesList.value[0].is_edit === 1;
      }
    } catch (e) {
      console.error('解析指标数据失败:', e);
      showFailToast('解析指标数据失败');
    }
  } else {
    // 原有的单指标模式
    indexId.value = route.query.index_id;
    indexName.value = route.query.index_name || '未知指标';
    showChart.value = route.query.is_chart === '1' || route.query.is_chart === 1;
    referenceMax.value = route.query.reference_max ? parseFloat(route.query.reference_max) : null;
    referenceMin.value = route.query.reference_min ? parseFloat(route.query.reference_min) : null;
    isEditable.value = route.query.is_edit === '1' || route.query.is_edit === 1;
  }
  
  // 对比模式下需要获取多个指标的数据
  if (compareMode.value) {
    fetchMultipleIndexDetails();
  } else {
    fetchIndexDetail();
  }
});

// 获取指标详情数据
const fetchIndexDetail = async () => {
  if (!indexName.value || indexName.value === '未知指标') {
    showFailToast('缺少指标名称参数');
    return;
  }

  try {
    loading.value = true;
    const response = await getMedicalIndexDetail(indexName.value);

    if (response.data.data && Array.isArray(response.data.data)) {
      tableData.value = response.data.data.sort((a, b) => new Date(a.medical_date) - new Date(b.medical_date));
    } else {
      tableData.value = [];
      showFailToast(response.message || '获取指标详情失败');
    }
  } catch (error) {
    console.error('获取指标详情失败:', error);
    showFailToast('网络错误，请稍后重试');
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 添加获取多个指标数据的方法
const fetchMultipleIndexDetails = async () => {
  if (indexesList.value.length === 0) {
    showFailToast('缺少指标参数');
    return;
  }

  try {
    loading.value = true;
    
    // 创建一个包含所有指标数据的对象
    const allIndexData = {};
    
    // 依次获取每个指标的数据
    for (const indexInfo of indexesList.value) {
      const response = await getMedicalIndexDetail(indexInfo.index_name);
      
      if (response.data.data && Array.isArray(response.data.data)) {
        // 为每个数据点添加指标名称标识
        const processedData = response.data.data.map(item => ({
          ...item,
          _index_name: indexInfo.index_name, // 添加指标名称标识
          _reference_max: indexInfo.reference_max ? parseFloat(indexInfo.reference_max) : null,
          _reference_min: indexInfo.reference_min ? parseFloat(indexInfo.reference_min) : null
        }));
        
        allIndexData[indexInfo.index_name] = processedData.sort((a, b) => 
          new Date(a.medical_date) - new Date(b.medical_date)
        );
      }
    }
    
    // 将第一个指标的数据设为主数据
    if (indexesList.value.length > 0 && allIndexData[indexesList.value[0].index_name]) {
      tableData.value = allIndexData[indexesList.value[0].index_name];
    }
    
    // 将所有数据传递给图表组件
    multiIndexData.value = allIndexData;
    
  } catch (error) {
    console.error('获取指标详情失败:', error);
    showFailToast('网络错误，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 监听视图切换，确保图表正确渲染
watch(currentView, (newView) => {
  if (newView === 'chart' && tableData.value.length > 0) {
    nextTick(() => {
      // 图表初始化已移至ChartView组件内部
    });
  }
});

// 强制对比模式下默认显示图表
watch(compareMode, (newVal) => {
  if (newVal) {
    currentView.value = 'chart';
  }
});

// 打开添加对话框
const openAddDialog = () => {
  isEditing.value = false;
  currentEditIndex.value = -1;
  formData.value = {
    medical_date: '',
    index_value: '',
    index_unit: '',
    reference_value: '',
    index_status: 'normal',
    comment: ''
  };
  showAddDialog.value = true;
};

// 编辑项目
const editItem = (item, index) => {
  isEditing.value = true;
  currentEditIndex.value = index;
  formData.value = {
    medical_id: item.medical_id,
    medical_date: item.medical_date,
    index_value: item.index_value,
    index_unit: item.index_unit || '',
    reference_value: item.reference_value || '',
    index_status: item.index_status || 'normal',
    comment: item.comment || ''
  };
  showAddDialog.value = true;
};

// 确认删除
const confirmDelete = (item, index) => {
  currentEditIndex.value = index;
  showDeleteConfirm.value = true;
};

// 删除数据
const deleteData = async () => {
  if (currentEditIndex.value >= 0 && currentEditIndex.value < tableData.value.length) {
    try {
      const item = tableData.value[currentEditIndex.value];
      if (!item.medical_id) {
        showNotify({ type: 'danger', message: '缺少医疗检查ID，无法删除' });
        return;
      }
      
      const response = await deleteMedicalCheckDetail(item.medical_id);
      
      if (response.data && response.data.status === 'success') {
        tableData.value.splice(currentEditIndex.value, 1);
        showSuccessToast('删除成功');
      } else {
        showNotify({ type: 'danger', message: response.data?.message || '删除失败' });
      }
    } catch (error) {
      console.error('删除数据失败:', error);
      showNotify({ type: 'danger', message: '删除失败，请稍后重试' });
    }
  }
};

// 保存数据
const saveData = async (data) => {
  try {
    if (isEditing.value && currentEditIndex.value >= 0) {
      // 编辑现有数据
      if (!isEditable.value) {
        // 仅编辑备注 - 调用API更新
        if (data.medical_id) {
          await updateMedicalCheckComment(data.medical_id, { comment: data.comment });
          tableData.value[currentEditIndex.value].comment = data.comment;
          showSuccessToast('备注更新成功');
        } else {
          showNotify({ type: 'danger', message: '缺少医疗检查ID，无法更新备注' });
          return;
        }
      } else {
        // 完全编辑
        tableData.value[currentEditIndex.value] = {
          ...tableData.value[currentEditIndex.value],
          ...data
        };
        showSuccessToast('更新成功');
      }
    } else if (isEditable.value) {
      // Adding new data
      try {
        // Prepare data for API
        const apiData = {
          index_name: indexName.value,
          medical_type: 3,
          hospital: '居家测量',
          ...data
        };
        
        // Call API to add new data
        const response = await addMedicalCheckDetail(apiData);
        
        if (response.data && response.data.status === 'success') {
          // Add the returned data (with ID) to the table
          tableData.value.push({
            ...data,
            medical_id: response.data.data.medical_id
          });
          showSuccessToast('添加成功');
        } else {
          showNotify({ type: 'danger', message: response.data?.message || '添加失败' });
          return;
        }
      } catch (error) {
        console.error('添加数据失败:', error);
        showNotify({ type: 'danger', message: '添加失败，请稍后重试' });
        return;
      }
    }

    // 关闭对话框
    showAddDialog.value = false;

    // 重置
    resetForm();

    // 重新排序数据（按日期）
    tableData.value.sort((a, b) => new Date(a.medical_date) - new Date(b.medical_date));
  } catch (error) {
    console.error('保存数据失败:', error);
    showNotify({ type: 'danger', message: '保存失败，请稍后重试' });
  }
};

// 重置表单
const resetForm = () => {
  formData.value = {
    medical_date: '',
    index_value: '',
    index_unit: '',
    reference_value: '',
    index_status: 'normal',
    comment: ''
  };
  isEditing.value = false;
  currentEditIndex.value = -1;
};
</script>

<style scoped>
.index-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .index-detail-container {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .index-detail-container {
    padding: 8px;
  }
}
</style>
