import axios from 'axios'

const service = axios.create({
  baseURL: 'http://127.0.0.1:5000',
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    config.headers['Authorization'] = localStorage.getItem('token') || ''
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    return Promise.reject(error)
  }
)

export default service