import { createStore } from 'vuex'

// 添加严格模式检测
const store = createStore({
  strict: process.env.NODE_ENV !== 'production'
})

export default createStore({
  state: {
    token: localStorage.getItem('token') || null,
    userInfo: JSON.parse(localStorage.getItem('userInfo')) || null
  },
  getters: {
    isAuthenticated: state => !!state.token,
    userId: state => state.userInfo ? state.userInfo.user_id : null,
    userName: state => state.userInfo ? state.userInfo.name : null,
    userPhone: state => state.userInfo ? state.userInfo.phone : null
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('token', token)
    },
    SET_USERINFO(state, userInfo) {
      state.userInfo = userInfo
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },
    CLEAR_AUTH(state) {
      state.token = '';
      state.userInfo = {};
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('loginTime'); // 新增：清除登录时间
    }
  },
  actions: {
    // 登录action可在此处添加
  }
})

