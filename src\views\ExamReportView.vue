<template>
  <div class="exam-report-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <back-button title="检查报告" />

    <!-- 搜索筛选区域 -->
    <div class="search-section" v-if="showSearch">
      <div class="search-card">
        <van-field
          v-model="searchForm.date"
          readonly
          clickable
          label="日期"
          placeholder="选择日期"
          @click="showDatePicker = true"
          right-icon="arrow-down"
        />
        <van-field
          v-model="searchForm.location"
          label="地点"
          placeholder="请输入检查地点"
          clearable
        />
        <div class="search-actions">
          <van-button type="primary" size="small" @click="searchReports">搜索</van-button>
          <van-button size="small" @click="resetSearch">重置</van-button>
        </div>
      </div>
    </div>

    <!-- 报告列表 -->
    <div class="list-section">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多数据了"
          loading-text="加载中..."
          @load="onLoad"
        >
          <div 
            v-for="item in examList" 
            :key="item.id" 
            class="exam-item"
          >
            <div class="exam-header">
              <div class="exam-title">{{ item.exam_type || '检查报告' }}</div>
              <div class="exam-date">{{ formatDate(item.medical_date) }}</div>
            </div>
            <div class="exam-info">
              <div class="info-item">
                <span class="label">医院：</span>
                <span class="value">{{ item.hospital || '未知' }}</span>
              </div>
            </div>
            <div class="exam-content" v-if="item.exam_info">
              <div class="content-label">检查信息：</div>
              <van-text-ellipsis 
                :content="item.exam_info" 
                :rows="3" 
                expand-text="展开" 
                collapse-text="收起"
                style="font-size: 14px;"
              />
            </div>
            <div class="exam-content" v-if="item.exam_diag">
              <div class="content-label">诊断信息：</div>
              <van-text-ellipsis 
                :content="item.exam_diag" 
                :rows="3" 
                expand-text="展开" 
                collapse-text="收起"
                style="font-size: 14px;"
              />
            </div>
          </div>
        </van-list>
        
        <!-- 添加回到顶部按钮 -->
        <van-back-top />

      </van-pull-refresh>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        title="选择日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

  </div>
</template>

<script setup>
import BackButton from '@/components/index-detail/BackButton.vue';
import { ref, reactive, onMounted } from 'vue';
import { showToast } from 'vant';
import { useRouter } from 'vue-router';
import { getMedicalExamList } from '@/api/medical';

const router = useRouter();

// 响应式数据
const examList = ref([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const showSearch = ref(false);
const showDatePicker = ref(false);

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 搜索表单
const searchForm = reactive({
  date: '',
  location: ''
});

// 方法
const onLoad = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    const response = await getMedicalExamList(params);
    
    if (response.data) {
      const newData = response.data.data || []; 
      
      // 修复分页逻辑
      pagination.total = response.data.pagination?.total || 0;
      
      if (refreshing.value) {
        examList.value = newData;
        refreshing.value = false;
      } else {
        examList.value = [...examList.value, ...newData];
      }
      // 判断是否还有更多数据
      finished.value = pagination.page >= pagination.total;
      pagination.page++;
    }
  } catch (error) {
    console.error('获取检验报告列表失败:', error);
    showToast('网络错误，请稍后重试');
    finished.value = true;
  } finally {
    loading.value = false;
  }
};

const onRefresh = async () => {
  try {
    refreshing.value = true;
    pagination.page = 1;
    finished.value = false;
    examList.value = [];
    await onLoad();
  } catch (error) {
    // showToast('刷新失败');
  } finally {
    refreshing.value = false;
  }
};

const searchReports = async () => {
  try {
    loading.value = true;
    const searchParams = {
      date: searchForm.date,
      location: searchForm.location
    };
    
    const response = await getMedicalExamList(searchParams);
    
    if (response.code === 200) {
      examList.value = response.data || [];
      finished.value = true; // 搜索结果不分页
      showToast('搜索完成');
    } else {
      showToast(response.message || '搜索失败');
    }
  } catch (error) {
    console.error('搜索失败:', error);
    showToast('搜索失败');
  } finally {
    loading.value = false;
  }
};

const resetSearch = () => {
  searchForm.date = '';
  searchForm.location = '';
  // 重置搜索后重新加载数据
  pagination.page = 1;
  finished.value = false;
  examList.value = [];
  onLoad();
};

// 同时修改 onDateConfirm 方法以正确处理数组格式
const onDateConfirm = (value) => {
  // value 现在是一个数组 [year, month, day]
  if (Array.isArray(value) && value.length >= 3) {
    const date = new Date(value[0], value[1], value[2]);
    searchForm.date = formatDate(date);
  }
  showDatePicker.value = false;
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

onMounted(() => {
  onLoad();
});
</script>

<style scoped>
.exam-report-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 动态背景元素 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 8%; left: 10%; }
.floating-cross:nth-child(2) { top: 20%; right: 15%; }
.floating-cross:nth-child(3) { top: 50%; left: 5%; }
.floating-cross:nth-child(4) { top: 70%; right: 10%; }
.floating-cross:nth-child(5) { top: 35%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 30%; left: 20%; }
.floating-cross:nth-child(7) { bottom: 10%; right: 25%; }
.floating-cross:nth-child(8) { top: 85%; left: 65%; }

.floating-circle {
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.06);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 15%; left: 55%; }
.floating-circle:nth-child(10) { top: 60%; right: 30%; }
.floating-circle:nth-child(11) { bottom: 20%; left: 70%; }
.floating-circle:nth-child(12) { top: 40%; right: 8%; }
.floating-circle:nth-child(13) { bottom: 40%; left: 40%; }
.floating-circle:nth-child(14) { top: 80%; right: 50%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.04);
  font-size: 16px;
  animation: heartbeat 5s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 25%; left: 25%; }
.floating-heart:nth-child(16) { top: 65%; right: 20%; }
.floating-heart:nth-child(17) { bottom: 15%; left: 75%; }
.floating-heart:nth-child(18) { top: 90%; left: 45%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-12px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.06; }
  50% { transform: scale(1.2); opacity: 0.12; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.05); }
  50% { transform: scale(1); }
  75% { transform: scale(1.02); }
}

/* 页面内容 */
.search-section {
  position: relative;
  z-index: 2;
  margin-bottom: 15px;
}

.search-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.search-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.list-section {
  position: relative;
  z-index: 2;
  height: calc(100vh - 50px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.exam-item {
  background: rgba(255, 255, 255, 0.95);
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.exam-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.exam-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c5aa0;
}

.exam-date {
  font-size: 12px;
  color: #8b9dc3;
}

.exam-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
}

.label {
  color: #6b7280;
  width: 50px;
  flex-shrink: 0;
}

.value {
  color: #374151;
  flex: 1;
}

.exam-content {
  margin-bottom: 10px;
}

.content-label {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 5px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .exam-report-container {
    padding: 10px;
  }
}
</style>
