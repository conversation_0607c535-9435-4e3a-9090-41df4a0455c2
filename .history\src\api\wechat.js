import request from './request';

// 获取微信JSSDK配置
export function getWxJsConfig(url) {
  return request({
    url: '/wechat/jsconfig',
    method: 'get',
    params: { url }
  });
}

// 初始化微信JSSDK
export function initWxJsSdk(callback) {
  // 获取当前页面URL（不包含#号后面的部分）
  const currentUrl = window.location.href.split('#')[0];
  
  // 请求后端获取签名配置
  getWxJsConfig(currentUrl).then(res => {
    if (res.status === 'success' && res.data) {
      const config = res.data;
      
      // 配置微信JSSDK
      wx.config({
        debug: process.env.NODE_ENV === 'development', // 开发环境下开启debug模式
        appId: config.appId,
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.signature,
        jsApiList: [
          'updateAppMessageShareData',
          'updateTimelineShareData',
          'onMenuShareTimeline',
          'onMenuShareAppMessage',
          'chooseImage',
          'previewImage'
        ]
      });
      
      // 注册成功回调
      wx.ready(() => {
        console.log('微信JSSDK初始化成功');
        if (typeof callback === 'function') {
          callback();
        }
      });
      
      // 注册失败回调
      wx.error(err => {
        console.error('微信JSSDK初始化失败:', err);
      });
    } else {
      console.error('获取微信配置失败:', res.message || '未知错误');
    }
  }).catch(error => {
    console.error('请求微信配置出错:', error);
  });
}